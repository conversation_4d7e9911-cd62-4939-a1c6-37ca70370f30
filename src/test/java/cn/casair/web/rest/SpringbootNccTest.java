package cn.casair.web.rest;

import cn.casair.CasairwebbaseApp;
import cn.casair.dto.nc.result.NcResultDTO;
import cn.casair.dto.nc.result.NccCustomerDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import nccloud.open.api.auto.token.itf.IAPIUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/12/03 14:45
 */
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CasairwebbaseApp.class)
public class SpringbootNccTest {

    @Autowired
    private IAPIUtils iAPIUtils;

    @Test
    public void getToken() throws Exception {
        log.info("token:{}", iAPIUtils.getToken());
    }

    @Test
    public void getApiUrl() throws Exception {
        log.info("getApiUrl:{}", iAPIUtils.getApiUrl());
//        log.info("token:{}", iAPIUtils.getToken());
        NcResultDTO<NccCustomerDTO> resultDTO = new NcResultDTO<>();
        NccCustomerDTO ncCustomerDTO = new NccCustomerDTO();
        ncCustomerDTO.setName("test");
        resultDTO.setErrorNo("0");
        resultDTO.setData(ncCustomerDTO);
        String json = JSON.toJSONString(resultDTO);
        log.info("resultDTO:{}", json);
        log.info("ncCustomerDTO:{}", JSON.parseObject(json, new TypeReference<NcResultDTO<NccCustomerDTO>>() {
        }));
        NcResultDTO<NccCustomerDTO> parseObject = JSON.parseObject(json, NcResultDTO.class);
        log.info("parseObject:{}", parseObject);
    }


}
