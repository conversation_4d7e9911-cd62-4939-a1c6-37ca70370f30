package cn.casair.dto.nc;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 凭证传输对象（DTO）
 * 用于调用 NCC 凭证生成接口：POST http://ip:port/uapws/rest/nccgl/voucher/insert
 * 对应场景：外部系统生成 NCC 凭证
 */
@Data
public class NccVoucherDTO {

    /**
     * 核算账簿编码
     * 示例值：ht0901-0001
     * 是否必传：是
     */
    @JsonProperty("accbook_code")
    private String accbookCode;

    /**
     * 制单日期，格式：yyyy-MM-dd
     * 示例值：2023-01-10
     * 是否必传：是
     */
    @JsonProperty("prepared_date")
    private String preparedDate;

    /**
     * 会计年度
     * 示例值：2023
     * 是否必传：是
     */
    @JsonProperty("year")
    private String year;

    /**
     * 会计期间
     * 示例值：01
     * 是否必传：是
     */
    @JsonProperty("period")
    private String period;

    /**
     * 凭证类别编码
     * 示例值：01
     * 是否必传：是
     */
    @JsonProperty("voucher_type")
    private String voucherType;

    /**
     * 制单人编码
     * 示例值：xx
     * 是否必传：是
     */
    @JsonProperty("prepared")
    private String prepared;

    /**
     * 凭证号
     * 示例值：4
     * 是否必传：否（不传时由 NC 系统自动生成）
     */
    @JsonProperty("num")
    private String num;

    /**
     * 附单据数
     * 示例值：0
     * 是否必传：否
     */
    @JsonProperty("attachment")
    private String attachment;

    /**
     * 凭证分录列表
     * 每条分录包含科目、金额、摘要、辅助核算等信息
     * 是否必传：是（至少一条分录）
     */
    @JsonProperty("detail")
    private List<Detail> detail;

    /**
     * 凭证分录 DTO（内部类）
     */
    @Data
    public static class Detail {

        /**
         * 分录号（序号）
         * 示例值：1
         * 是否必传：是
         */
        @JsonProperty("detail_index")
        private String detailIndex;

        /**
         * 摘要
         * 示例值：新增
         * 是否必传：是
         */
        @JsonProperty("explanation")
        private String explanation;

        /**
         * 科目编码
         * 示例值：1001
         * 是否必传：是
         */
        @JsonProperty("account_code")
        private String accountCode;

        /**
         * 币种编码
         * 示例值：CNY
         * 是否必传：是
         */
        @JsonProperty("currtype_code")
        private String currtypeCode;

        /**
         * 原币金额
         * 示例值：100
         * 是否必传：是
         */
        @JsonProperty("amount")
        private BigDecimal amount;

        /**
         * 组织本币借方金额
         * 示例值：100
         * 是否必传：是（仅借方分录填写）
         */
        @JsonProperty("local_debit_amount")
        private BigDecimal localDebitAmount;

        /**
         * 组织本币贷方金额
         * 示例值：100
         * 是否必传：是（仅贷方分录填写）
         */
        @JsonProperty("local_credit_amount")
        private BigDecimal localCreditAmount;

        /**
         * 业务日期，格式：yyyy-MM-dd
         * 示例值：2023-01-10
         * 是否必传：否（但建议传入，与制单日期一致或按业务发生日）
         */
        @JsonProperty("busi_date")
        private String busiDate;

        /**
         * 辅助核算项列表
         * 示例：客户、部门、项目等
         * 是否必传：否（根据科目是否启用辅助核算决定）
         */
        @JsonProperty("ass")
        private List<Ass> ass;
    }

    /**
     * 辅助核算项 DTO（内部类）
     */
    @Data
    public static class Ass {

        /**
         * 辅助核算项编码（如：0007 表示客户，0004 表示部门等）
         * 示例值：0007
         * 是否必传：是
         */
        @JsonProperty("check_type_code")
        private String checkTypeCode;

        /**
         * 辅助核算值编码
         * 特殊说明：当 check_type_code 为特定值（如代表“客商”的编码）时，
         * 此处应传递“名称”而非编码；其他情况传递“辅助核算值编码”
         * 示例值：1111 或 客户名称
         * 是否必传：是
         */
        @JsonProperty("check_value_code")
        private String checkValueCode;
    }
}
