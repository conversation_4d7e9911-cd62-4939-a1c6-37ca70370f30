package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.common.enums.*;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.*;
import cn.casair.common.utils.excel.CellItem;
import cn.casair.common.utils.excel.ExcelReadUtils;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.dto.billl.BillFieldInfoDTO;
import cn.casair.dto.billl.BillTableHeaderDTO;
import cn.casair.dto.billl.ExcelParseDynamicFieldsDTO;
import cn.casair.mapper.HrBillDetailMapper;
import cn.casair.mapper.HrBillMapper;
import cn.casair.mapper.HrBillTotalMapper;
import cn.casair.mapper.HrFeeReviewMapper;
import cn.casair.repository.*;
import cn.casair.service.*;
import cn.casair.service.component.ecloud.ECloudComponent;
import cn.casair.web.rest.util.ResponseUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 费用审核服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-02
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrFeeReviewServiceImpl extends ServiceImpl<HrFeeReviewRepository, HrFeeReview> implements HrFeeReviewService {

    private final HrProtocolRepository hrProtocolRepository;
    private final HrStaffEmolumentRepository hrStaffEmolumentRepository;
    private final HrWelfareCompensationRepository hrWelfareCompensationRepository;
    private final HrStaffTurnPositiveRepository hrStaffTurnPositiveRepository;
    private final HrBillRepository hrBillRepository;
    private final HrFeeReviewRepository hrFeeReviewRepository;
    private final HrFeeReviewMapper hrFeeReviewMapper;
    private final HrClientService hrClientService;
    private final HrClientRepository hrClientRepository;
    private final SysOperLogService sysOperLogService;
    private final HrArrivalRecordService hrArrivalRecordService;
    private final HrBillInvoiceRepository hrBillInvoiceRepository;
    private final HrBillReimbursementApplyRepository hrBillReimbursementApplyRepository;
    private final HrBillService hrBillService;
    private final HrBillDetailRepository hrBillDetailRepository;
    private final HrBillDetailService hrBillDetailService;
    private final HrBillMapper hrBillMapper;
    private final HrBillTotalRepository hrBillTotalRepository;
    private final HrBillTotalMapper hrBillTotalMapper;
    private final HrBillDetailMapper hrBillDetailMapper;
    private final HrBillInvoiceService hrBillInvoiceService;
    private final HrBillInvoiceReviewRepository hrBillInvoiceReviewRepository;
    private final HrFeeReviewConfigRepository hrFeeReviewConfigRepository;
    private final HrBillDynamicFieldsService hrBillDynamicFieldsService;
    private final HrAppendixService hrAppendixService;
    private final HrTalentStaffRepository hrTalentStaffRepository;
    private final HrSealsRepository hrSealsRepository;
    private final ECloudComponent eCloudComponent;

    /**
     * 创建费用审核
     *
     * @param hrFeeReviewDTO
     * @return
     */
    @Override
    public HrFeeReviewDTO createHrFeeReview(HrFeeReviewDTO hrFeeReviewDTO) {
        log.info("Create new HrFeeReview:{}", hrFeeReviewDTO);
        HrFeeReview hrFeeReview = this.hrFeeReviewMapper.toEntity(hrFeeReviewDTO);
        hrFeeReview.setLastModifiedDate(LocalDateTime.now());
        this.hrFeeReviewRepository.insert(hrFeeReview);

        this.dealUpdateBillReviewState(hrFeeReview.getClientId(), hrFeeReview.getBillId(), BillEnum.FeeReviewState.TO_BE_REVIEWED.getKey());
        HrClient hrClient=this.hrClientRepository.selectById(hrFeeReviewDTO.getClientId());
        hrFeeReviewDTO.setClientName(hrClient.getClientName());
        // 操作日志
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.FEE_REVIEW.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrFeeReviewDTO),
            HrFeeReviewDTO.class,
            null,
            JSON.toJSONString(hrFeeReview)
        );
        return this.hrFeeReviewMapper.toDto(hrFeeReview);
    }

    /**
     * 处理账单最终审核状态更新
     *
     * @param clientId
     * @param billIdStr
     * @return void
     * <AUTHOR>
     * @date 2021/12/16
     **/
    private void dealUpdateBillReviewState(String clientId, String billIdStr, Integer reviewState) {
        List<HrClient> clientIds = this.hrProtocolRepository.selectAllParentClientById(clientId);
        Set<String> clientIdList = new LinkedHashSet<>();
        if (!clientIds.isEmpty()) {
            for (HrClient client : clientIds) {
                clientIdList.add(client.getId());
                List<HrClient> clientIdLists = this.hrProtocolRepository.selectAllParentClientById(client.getParentId());
                for (HrClient idList : clientIdLists) {
                    clientIdList.add(idList.getId());
                }
                if (!clientIdLists.isEmpty()) {
                    for (HrClient client1 : clientIdLists) {
                        List<HrClient> hrClientList = this.hrProtocolRepository.selectAllParentClientById(client1.getParentId());
                        for (HrClient hrClient : hrClientList) {
                            clientIdList.add(hrClient.getId());
                        }
                    }
                }
            }
        }
        clientIdList.remove(clientId);

        List<String> billIdList = Arrays.asList(billIdStr.split(","));
        AtomicInteger count = new AtomicInteger();
        if (!clientIdList.isEmpty()) {
            clientIdList.forEach(tempClientId -> billIdList.forEach(billId -> count.addAndGet(this.hrFeeReviewRepository.selectByClientIdAndBillId(tempClientId, billId))));
        }
        if (count.get() == 0) {
            this.hrBillRepository.updateBillReviewState(billIdList, reviewState);
        }
    }

    /**
     * 客户审核费用审核
     *
     * @param hrFeeReviewDTO
     * @return
     */
    @Override
    public Optional<HrFeeReviewDTO> updateHrFeeReview(HrFeeReviewDTO hrFeeReviewDTO) {
        return Optional.ofNullable(this.hrFeeReviewRepository.selectById(hrFeeReviewDTO.getId()))
            .map(roleTemp -> {
                HrFeeReview hrFeeReview = this.hrFeeReviewMapper.toEntity(hrFeeReviewDTO);
                this.hrFeeReviewRepository.updateById(hrFeeReview);
                List<String> billList = Arrays.asList(roleTemp.getBillId().split(","));
                List<HrBill> hrBills = hrBillRepository.selectBatchIds(billList);
                if (hrFeeReviewDTO.getStatus() == 1) {
                    this.hrBillRepository.updateReviewStateAndBillState(billList,BillEnum.FeeReviewState.APPROVED.getKey(), BillEnum.BillState.LOCKED.getKey());
                    hrBillRepository.updateSalaryPaymentState(billList,1);
                    // 补差设置为应用
                    this.hrWelfareCompensationRepository.updateIsUsed(billList, 1);
                    // 更新员工福利配置缴费年月
                    this.hrStaffEmolumentRepository.updatePaymentDateByBillId(roleTemp);
                    HrBillTotal hrBillTotal = hrBillTotalRepository.selectOne(new QueryWrapper<HrBillTotal>().eq("bill_id", roleTemp.getId()).last("LIMIT 1"));
                    HrClient hrClient = hrClientRepository.selectById(roleTemp.getClientId());
                    HrClient rootParentClient = hrClientRepository.getRootParentClient(roleTemp.getClientId());
                    List<HrBill> salaryList = hrBills.stream().filter(lst -> lst.getBillType().equals(BillEnum.BillType.SALARY_BILL.getKey())).collect(Collectors.toList());
                    //费用审核自动化
                    if (hrFeeReviewDTO.getIsDrawBill() == 0){
                        /*boolean flag = true;
                        if (hrBills.get(0).getBillType().equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey())){
                            flag = false;
                        }*/
                        //自动创建到账记录以及报销申请
                        this.hrBillInvoiceService.saveArrivalAndReimbursement(roleTemp,hrBillTotal,hrClient,hrBills,0, null,rootParentClient);
                    }else {
                        //添加开票申请
                        HrBillInvoiceDTO hrBillInvoiceDTO = new HrBillInvoiceDTO();
                        hrBillInvoiceDTO.setClientId(roleTemp.getClientId()).setBillId(roleTemp.getId()).setApplyDate(LocalDate.now())
                            .setInvoiceType(1).setApproveStatus(BillInvoiceApproveEnums.NON_AUDITABLE.getKey())
                            .setInvoiceState(BillInvoiceApproveEnums.InvoiceState.OPENABLE_INVOICE.getKey())
                            .setInvoiceLockState(BillInvoiceApproveEnums.InvoiceLockState.NOT_LOCKED.getKey());
                        List<HrBillInvoiceRecordDTO> invoiceRecords = new ArrayList<>();
                        if (hrBills.get(0).getBillType().equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey())){
                            HrBillInvoiceRecordDTO hrBillInvoiceRecordDTO = hrBillInvoiceService.assignmentBillInvoiceRecord(hrClient,hrBillTotal.getTotal(),
                                BillInvoiceApproveEnums.InvoiceContent.TECHNOLOGY_SERVICE_FEE.getKey(),BillInvoiceApproveEnums.InvoiceContent.TECHNOLOGY_SERVICE_FEE.getValue(), roleTemp.getPayYear(), roleTemp.getPayMonthly());
                            hrBillInvoiceRecordDTO.setFeeReviewIds(Collections.singletonList(roleTemp.getId()));
                            invoiceRecords.add(hrBillInvoiceRecordDTO);
                        }else {
                            HrProtocol hrProtocol = hrBillService.checkProtocol(roleTemp.getClientId());
                            if (hrProtocol.getInvoiceType() == 1){//按分项金额开票
                                if (rootParentClient.getId().equals(SpecialBillClient.HAIER.getKey())){
                                    //工资收入：税后实发工资
                                    if (hrBillTotal.getRealSalaryTotal() != null && !BigDecimalCompare.of(hrBillTotal.getRealSalaryTotal()).eq(BigDecimal.ZERO)){
                                        HrBillInvoiceRecordDTO hrBillInvoiceRecordDTO = hrBillInvoiceService.assignmentBillInvoiceRecord(hrClient,hrBillTotal.getRealSalaryTotal(),
                                            BillInvoiceApproveEnums.InvoiceContent.WAGE_INCOME.getKey(),BillInvoiceApproveEnums.InvoiceContent.WAGE_INCOME.getValue(), roleTemp.getPayYear(), roleTemp.getPayMonthly());
                                        hrBillInvoiceRecordDTO.setFeeReviewIds(Collections.singletonList(roleTemp.getId()));
                                        invoiceRecords.add(hrBillInvoiceRecordDTO);
                                    }
                                    //海尔工贸代理费收入：代理费
                                    if (hrBillTotal.getServiceFeeTotal() != null && !BigDecimalCompare.of(hrBillTotal.getServiceFeeTotal()).eq(BigDecimal.ZERO)){
                                        HrBillInvoiceRecordDTO hrBillInvoiceRecordDTO = hrBillInvoiceService.assignmentBillInvoiceRecord(hrClient,hrBillTotal.getServiceFeeTotal(),
                                            BillInvoiceApproveEnums.InvoiceContent.HAIER_INDUSTRY_INCOME.getKey(),BillInvoiceApproveEnums.InvoiceContent.HAIER_INDUSTRY_INCOME.getValue(), roleTemp.getPayYear(), roleTemp.getPayMonthly());
                                        hrBillInvoiceRecordDTO.setFeeReviewIds(Collections.singletonList(roleTemp.getId()));
                                        invoiceRecords.add(hrBillInvoiceRecordDTO);
                                    }
                                    //社保公积金：由社保、公积金和员工个税构成
                                    //社保总金额 - 单位社保补差-单位其他-个人社保补差-个人其他
                                    BigDecimal socialSecurityTotal = CalculateUtils.decimalListSubstract(hrBillTotal.getSocialSecurityTotal(),
                                        hrBillTotal.getUnitSocialSecurityMakeUpTotal(),hrBillTotal.getUnitOtherFeeTotal(),
                                        hrBillTotal.getPersonalSocialSecurityMakeUpTotal(),hrBillTotal.getPersonalOtherFeeTotal()
                                    );
                                    BigDecimal addition = CalculateUtils.decimalListAddition(socialSecurityTotal, hrBillTotal.getAccumulationFundTotal(), hrBillTotal.getPersonalTaxTotal());
                                    if (!BigDecimalCompare.of(addition).eq(BigDecimal.ZERO)){
                                        HrBillInvoiceRecordDTO hrBillInvoiceRecordDTO = hrBillInvoiceService.assignmentBillInvoiceRecord(hrClient,addition,
                                            BillInvoiceApproveEnums.InvoiceContent.SOCIAL_SECURITY_ACCUMULATION_FUND.getKey(),BillInvoiceApproveEnums.InvoiceContent.SOCIAL_SECURITY_ACCUMULATION_FUND.getValue(), roleTemp.getPayYear(), roleTemp.getPayMonthly());
                                        hrBillInvoiceRecordDTO.setFeeReviewIds(Collections.singletonList(roleTemp.getId()));
                                        invoiceRecords.add(hrBillInvoiceRecordDTO);
                                    }
                                }else {
                                    if (hrBillTotal.getAccumulationFundTotal()!=null && !BigDecimalCompare.of(hrBillTotal.getAccumulationFundTotal()).eq(BigDecimal.ZERO)){
                                        HrBillInvoiceRecordDTO hrBillInvoiceRecordDTO = hrBillInvoiceService.assignmentBillInvoiceRecord(hrClient,hrBillTotal.getAccumulationFundTotal(),
                                            BillInvoiceApproveEnums.InvoiceContent.ACCUMULATION_FUND_INCOME.getKey(),BillInvoiceApproveEnums.InvoiceContent.ACCUMULATION_FUND_INCOME.getValue(), roleTemp.getPayYear(), roleTemp.getPayMonthly());
                                        hrBillInvoiceRecordDTO.setFeeReviewIds(Collections.singletonList(roleTemp.getId()));
                                        invoiceRecords.add(hrBillInvoiceRecordDTO);
                                    }
                                    if (hrBillTotal.getSocialSecurityTotal()!=null && !BigDecimalCompare.of(hrBillTotal.getSocialSecurityTotal()).eq(BigDecimal.ZERO)){
                                        HrBillInvoiceRecordDTO hrBillInvoiceRecordDTO = hrBillInvoiceService.assignmentBillInvoiceRecord(hrClient,hrBillTotal.getSocialSecurityTotal(),
                                            BillInvoiceApproveEnums.InvoiceContent.SOCIAL_SECURITY_INCOME.getKey(),BillInvoiceApproveEnums.InvoiceContent.SOCIAL_SECURITY_INCOME.getValue(), roleTemp.getPayYear(), roleTemp.getPayMonthly());
                                        hrBillInvoiceRecordDTO.setFeeReviewIds(Collections.singletonList(roleTemp.getId()));
                                        invoiceRecords.add(hrBillInvoiceRecordDTO);
                                    }
                                    if (CollectionUtils.isNotEmpty(salaryList)){
                                        HrBillDetailDTO hrBillDetailDTO = hrBillDetailRepository.getPreTaxSalaryTotal(billList);
                                        if (hrBillDetailDTO!= null && hrBillDetailDTO.getPreTaxSalary()!=null && !BigDecimalCompare.of(hrBillDetailDTO.getPreTaxSalary()).eq(BigDecimal.ZERO)){
                                            HrBillInvoiceRecordDTO hrBillInvoiceRecordDTO = hrBillInvoiceService.assignmentBillInvoiceRecord(hrClient,hrBillDetailDTO.getPreTaxSalary(),
                                                BillInvoiceApproveEnums.InvoiceContent.WAGE_INCOME.getKey(),BillInvoiceApproveEnums.InvoiceContent.WAGE_INCOME.getValue(), roleTemp.getPayYear(), roleTemp.getPayMonthly());
                                            hrBillInvoiceRecordDTO.setFeeReviewIds(Collections.singletonList(roleTemp.getId()));
                                            invoiceRecords.add(hrBillInvoiceRecordDTO);
                                        }else {
                                            //薪酬账单按全年一次性奖金计税开票取总金额
                                            HrBillDetailDTO totalBySalaryBill = hrBillDetailRepository.getTotalBySalaryBill(billList);
                                            if (totalBySalaryBill!= null && totalBySalaryBill.getTotal() != null && !BigDecimalCompare.of(totalBySalaryBill.getTotal()).eq(BigDecimal.ZERO)){
                                                HrBillInvoiceRecordDTO hrBillInvoiceRecordDTO = hrBillInvoiceService.assignmentBillInvoiceRecord(hrClient,totalBySalaryBill.getTotal(),
                                                    BillInvoiceApproveEnums.InvoiceContent.WAGE_INCOME.getKey(),BillInvoiceApproveEnums.InvoiceContent.WAGE_INCOME.getValue(), roleTemp.getPayYear(), roleTemp.getPayMonthly());
                                                hrBillInvoiceRecordDTO.setFeeReviewIds(Collections.singletonList(roleTemp.getId()));
                                                invoiceRecords.add(hrBillInvoiceRecordDTO);
                                            }
                                        }
                                    }
                                    if (hrBillTotal.getServiceFeeTotal()!=null && !BigDecimalCompare.of(hrBillTotal.getServiceFeeTotal()).eq(BigDecimal.ZERO)){
                                        BigDecimal serviceFeeTotal = CalculateUtils.decimalAddition(hrBillTotal.getServiceFeeTotal(),hrBillTotal.getTaxationFeeTotal());
                                        HrBillInvoiceRecordDTO hrBillInvoiceRecordDTO = hrBillInvoiceService.assignmentBillInvoiceRecord(hrClient, serviceFeeTotal,
                                            BillInvoiceApproveEnums.InvoiceContent.AGENCY_FEE_INCOME.getKey(),BillInvoiceApproveEnums.InvoiceContent.AGENCY_FEE_INCOME.getValue(), roleTemp.getPayYear(), roleTemp.getPayMonthly());
                                        hrBillInvoiceRecordDTO.setFeeReviewIds(Collections.singletonList(roleTemp.getId()));
                                        invoiceRecords.add(hrBillInvoiceRecordDTO);
                                    }
                                }
                            }
                            //按总金额开票
                            else {
                                if (hrBillTotal.getTotal()!=null && !BigDecimalCompare.of(hrBillTotal.getTotal()).eq(BigDecimal.ZERO)){
                                    HrBillInvoiceRecordDTO hrBillInvoiceRecordDTO = hrBillInvoiceService.assignmentBillInvoiceRecord(hrClient,hrBillTotal.getTotal(),
                                        BillInvoiceApproveEnums.InvoiceContent.HUMAN_RESOURCES_SERVICE_FEE.getKey(),BillInvoiceApproveEnums.InvoiceContent.HUMAN_RESOURCES_SERVICE_FEE.getValue(), roleTemp.getPayYear(), roleTemp.getPayMonthly());
                                    hrBillInvoiceRecordDTO.setFeeReviewIds(Collections.singletonList(roleTemp.getId()));
                                    invoiceRecords.add(hrBillInvoiceRecordDTO);
                                }
                            }
                            List<HrBill> hrBillList = hrBillRepository.selectList(new QueryWrapper<HrBill>().in("id", billList).eq("bill_type", BillEnum.BillType.OTHER_BILL.getKey()).eq("is_delete", 0));
                            if (CollectionUtils.isNotEmpty(hrBillList)){
                                List<String> billIds = hrBillList.stream().map(HrBill::getId).collect(Collectors.toList());
                                List<HrBillTotal> billTotalList = hrBillTotalRepository.selectList(new QueryWrapper<HrBillTotal>().in("bill_id", billIds));
                                BigDecimal bigDecimal = billTotalList.stream().map(HrBillTotal::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
                                HrBillInvoiceRecordDTO hrBillInvoiceRecordDTO = hrBillInvoiceService.assignmentBillInvoiceRecord(hrClient,bigDecimal,
                                    BillInvoiceApproveEnums.InvoiceContent.OTHER_EXPENSES.getKey(),BillInvoiceApproveEnums.InvoiceContent.OTHER_EXPENSES.getValue(), roleTemp.getPayYear(), roleTemp.getPayMonthly());
                                hrBillInvoiceRecordDTO.setFeeReviewIds(Collections.singletonList(roleTemp.getId()));
                                invoiceRecords.add(hrBillInvoiceRecordDTO);
                            }
                        }
                        //社会治理上月结余
                        if (rootParentClient.getId().equals(SpecialBillClient.SOCIAL_GOVERNANCE.getKey()) && CollectionUtils.isNotEmpty(salaryList)){
                            this.socialGovernanceBalance(roleTemp,hrClient,salaryList,invoiceRecords);
                        }
                        if (CollectionUtils.isNotEmpty(invoiceRecords)){
                            double totalAmount = invoiceRecords.stream().collect(Collectors.summarizingDouble(value -> value.getTotalAmount())).getSum();
                            double taxAmount = invoiceRecords.stream().collect(Collectors.summarizingDouble(value -> value.getTaxAmount())).getSum();
                            hrBillInvoiceDTO.setTotalAmount(totalAmount);
                            hrBillInvoiceDTO.setTaxAmount(taxAmount);
                        }
                        hrBillInvoiceDTO.setInvoiceRecords(invoiceRecords).setFlag(false);
                        hrBillInvoiceService.createHrBillInvoice(hrBillInvoiceDTO);
                    }
                } else if (hrFeeReviewDTO.getStatus() == 2) {
                    this.hrBillRepository.updateReviewStateAndBillState(billList,BillEnum.FeeReviewState.AUDIT_REJECT.getKey(), BillEnum.BillState.NOT_LOCKED.getKey());
                }
                log.info("Update HrFeeReview:{}", hrFeeReviewDTO);
                return hrFeeReviewDTO;
            });
    }

    /**
     * 社会治理开票内容添加社会治理
     * @param hrFeeReview
     * @param hrClient
     * @param hrBills
     * @param invoiceRecords
     */
    private void socialGovernanceBalance(HrFeeReview hrFeeReview, HrClient hrClient, List<HrBill> hrBills, List<HrBillInvoiceRecordDTO> invoiceRecords) {
        List<String> clientIds = hrBills.stream().map(HrBill::getClientId).distinct().collect(Collectors.toList());
        // 上个缴费年月
        int lastPayYear;
        int lastPayMonthly;
        if (hrFeeReview.getPayMonthly() > 1) {
            lastPayYear = hrFeeReview.getPayYear();
            lastPayMonthly = hrFeeReview.getPayMonthly() - 1;
        } else {
            lastPayYear = hrFeeReview.getPayYear() - 1;
            lastPayMonthly = 12;
        }
        QueryWrapper<HrBill> wrapper = new QueryWrapper<>();
        wrapper.eq("pay_year",lastPayYear)
            .eq("pay_monthly",lastPayMonthly)
            .eq("bill_type", BillEnum.BillType.SALARY_BILL.getKey())
            .eq("bill_state", BillEnum.BillState.LOCKED.getKey())
            .eq("is_official",1).eq("review_state",1)
            .in("client_id",clientIds);
        List<HrBill> hrBillList = hrBillRepository.selectList(wrapper);
        Map<Integer, List<HrBill>> listMap = hrBillList.stream().collect(Collectors.groupingBy(HrBill::getBillPurpose));
        BigDecimal invoiceAmount = BigDecimal.ZERO;
        BigDecimal salaryAmount = BigDecimal.ZERO;
        List<HrBill> invoiceBillList = listMap.get(1);//开票申请账单中的实发工资
        if (CollectionUtils.isNotEmpty(invoiceBillList)){
            List<String> collect = invoiceBillList.stream().map(HrBill::getId).collect(Collectors.toList());
            List<HrBillDetailDTO> list = hrBillDetailRepository.getListByBillIdBatch(collect, 1);
            invoiceAmount = list.stream().map(HrBillDetailDTO::getRealSalary).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        List<HrBill> salaryBillList = listMap.get(2);//工资发放账单中的实发工资
        if (CollectionUtils.isNotEmpty(salaryBillList)){
            List<String> collect = salaryBillList.stream().map(HrBill::getId).collect(Collectors.toList());
            List<HrBillDetailDTO> list = hrBillDetailRepository.getListByBillIdBatch(collect, 1);
            salaryAmount = list.stream().map(HrBillDetailDTO::getRealSalary).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        //“开票申请账单中的实发工资-工资发放账单中的实发工资”就为上月结余
        HrBillInvoiceRecordDTO hrBillInvoiceRecordDTO = hrBillInvoiceService.assignmentBillInvoiceRecord(hrClient, CalculateUtils.decimalSubtraction(invoiceAmount, salaryAmount),
            BillInvoiceApproveEnums.InvoiceContent.LAST_MONTH_BALANCE.getKey(),BillInvoiceApproveEnums.InvoiceContent.LAST_MONTH_BALANCE.getValue(), hrFeeReview.getPayYear(), hrFeeReview.getPayMonthly());
        hrBillInvoiceRecordDTO.setFeeReviewIds(Collections.singletonList(hrFeeReview.getId()));
        invoiceRecords.add(hrBillInvoiceRecordDTO);
    }

    @Override
    public Object getHrFeeReviewStaff(HrFeeReviewDTO hrFeeReviewDTO) {
        List<String> clientIdLista = new ArrayList<>();
        List<String> clientIdList = this.hrFeeReviewRepository.selectChlientId(hrFeeReviewDTO.getClientId());
        clientIdLista.addAll(clientIdList);
        if (CollectionUtils.isNotEmpty(clientIdList)) {
            for (String s : clientIdList) {
                if (CollectionUtils.isNotEmpty(clientIdList)) {
                    List<String> clientIdLists = this.hrFeeReviewRepository.selectChlientId(s);
                    clientIdLista.addAll(clientIdLists);
                    if (CollectionUtils.isNotEmpty(clientIdLists)) {
                        for (String idList : clientIdLists) {
                            List<String> clientIdListss = this.hrFeeReviewRepository.selectChlientId(idList);
                            clientIdLista.addAll(clientIdListss);
                        }
                    }

                }
            }
        }
        clientIdLista.add(hrFeeReviewDTO.getClientId());
        HrFeeReviewDTO hrFeeReviewDTOs = new HrFeeReviewDTO();
        hrFeeReviewDTO.setClientIdList(clientIdLista);
        hrFeeReviewDTOs = this.hrFeeReviewRepository.selectClientname(hrFeeReviewDTO.getClientId());
        if (hrFeeReviewDTOs != null) {
            if (hrFeeReviewDTO.getManner().equals(1)) {
                hrFeeReviewDTOs.setHrClientDTO(this.hrFeeReviewRepository.selectChlient(clientIdLista));
                QueryWrapper<HrBill> we = new QueryWrapper<>();
                we.select("id");
                we.in("client_id", clientIdLista);
                we.eq("pay_year", hrFeeReviewDTO.getPayYear());
                we.eq("pay_monthly", hrFeeReviewDTO.getPayMonthly());
                we.eq("is_official", 1);
                List<HrBill> hrBill = this.hrBillRepository.selectList(we);
                List<String> billId = hrBill.stream().map(HrBill::getId).distinct().collect(Collectors.toList());
                //有序没去冲
                ArrayList<HrBillDetailDTO> hrBillDetailDTOS = this.hrFeeReviewRepository.selectBillDetails(billId);

                //集合全部相同的去重

                //List<HrBillDetailDTO> hrBillDetailDTOList = hrBillDetailDTOS.stream().distinct().collect(Collectors.toList());

                //集合员工id相同的去重
                ArrayList<HrBillDetailDTO> hrBillDetailDTOList = hrBillDetailDTOS.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getCertificateNum()))), ArrayList::new));
               LinkedList<HrBillDetailDTO>linkedList=new LinkedList<>();
                for (HrBillDetailDTO hrBillDetailDTO : hrBillDetailDTOS) {
                    for (HrBillDetailDTO billDetailDTO : hrBillDetailDTOList) {
                        if (hrBillDetailDTO.getCertificateNum()==billDetailDTO.getCertificateNum()){
                            linkedList.add(billDetailDTO);
                        }
                    }
                }
                ArrayList<HrBillDetailDTO> linkedLists = linkedList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getCertificateNum()))), ArrayList::new));
                LinkedList<HrBillDetailDTO>linkedListsa=new LinkedList<>();
                linkedListsa.addAll(linkedLists);
                hrFeeReviewDTOs.setHrBillDetailDTO(linkedListsa);
                for (HrBillDetailDTO hrBillDetailDTO : hrFeeReviewDTOs.getHrBillDetailDTO()) {
                    List<HrBillDetailItemsDTO> hrBillDetailItemsDTOList = this.hrFeeReviewRepository.selectHrBillDetailItems(hrBillDetailDTO.getId());
                    hrBillDetailDTO.setHrBillDetailItemsList(hrBillDetailItemsDTOList);
                }
                hrFeeReviewDTOs.setHrBillTotalDTO(this.hrFeeReviewRepository.selectBillTotal(hrFeeReviewDTO));
                hrFeeReviewDTOs.setHrBillDTO(this.hrFeeReviewRepository.selectBill(hrFeeReviewDTO));
            } else {
                hrFeeReviewDTOs = this.hrFeeReviewRepository.selectTile(hrFeeReviewDTO.getId());
                hrFeeReviewDTO.setPayYear(hrFeeReviewDTOs.getPayYear());
                hrFeeReviewDTO.setPayMonthly(hrFeeReviewDTOs.getPayMonthly());
                hrFeeReviewDTOs.setHrClientDTO(this.hrFeeReviewRepository.selectChlient(clientIdLista));
                QueryWrapper<HrBill> we = new QueryWrapper<>();
                we.select("id");
                we.in("client_id", clientIdLista);
                we.eq("pay_year", hrFeeReviewDTO.getPayYear());
                we.eq("pay_monthly", hrFeeReviewDTO.getPayMonthly());
                we.eq("is_official", 1);
                List<HrBill> hrBill = this.hrBillRepository.selectList(we);
                List<String> billId = hrBill.stream().map(HrBill::getId).distinct().collect(Collectors.toList());
                //有序没去冲
                ArrayList<HrBillDetailDTO> hrBillDetailDTOS = this.hrFeeReviewRepository.selectBillDetails(billId);

                //集合全部相同的去重

                //List<HrBillDetailDTO> hrBillDetailDTOList = hrBillDetailDTOS.stream().distinct().collect(Collectors.toList());

                //集合员工id相同的去重
                ArrayList<HrBillDetailDTO> hrBillDetailDTOList = hrBillDetailDTOS.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getCertificateNum()))), ArrayList::new));
                LinkedList<HrBillDetailDTO>linkedList=new LinkedList<>();
                for (HrBillDetailDTO hrBillDetailDTO : hrBillDetailDTOS) {
                    for (HrBillDetailDTO billDetailDTO : hrBillDetailDTOList) {
                        if (hrBillDetailDTO.getCertificateNum()==billDetailDTO.getCertificateNum()){
                            linkedList.add(billDetailDTO);
                        }
                    }
                }
                ArrayList<HrBillDetailDTO> linkedLists = linkedList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getCertificateNum()))), ArrayList::new));
                LinkedList<HrBillDetailDTO>linkedListsa=new LinkedList<>();
                linkedListsa.addAll(linkedLists);
                hrFeeReviewDTOs.setHrBillDetailDTO(linkedListsa);
                for (HrBillDetailDTO hrBillDetailDTO : hrFeeReviewDTOs.getHrBillDetailDTO()) {
                    List<HrBillDetailItemsDTO> hrBillDetailItemsDTOList = this.hrFeeReviewRepository.selectHrBillDetailItems(hrBillDetailDTO.getId());
                    hrBillDetailDTO.setHrBillDetailItemsList(hrBillDetailItemsDTOList);
                }
                hrFeeReviewDTOs.setHrBillTotalDTO(this.hrFeeReviewRepository.selectBillTotalas(hrFeeReviewDTO));
                hrFeeReviewDTOs.setHrBillDTO(this.hrFeeReviewRepository.selectBill(hrFeeReviewDTO));
            }
            List<HrAppendix> AppendixId = new ArrayList<>();
            if (hrFeeReviewDTOs.getSummaryAppendixId() != null) {
                List<String> DetailAppendixIds = Arrays.asList(hrFeeReviewDTOs.getSummaryAppendixId().split(","));
                AppendixId = this.hrStaffTurnPositiveRepository.selectAppendixId(DetailAppendixIds);
                hrFeeReviewDTOs.setSummaryAppendixIdUrl(AppendixId);
            }

            if (hrFeeReviewDTOs.getAppendixId() != null) {
                List<String> AppendixIds = Arrays.asList(hrFeeReviewDTOs.getAppendixId().split(","));
                AppendixId = this.hrStaffTurnPositiveRepository.selectAppendixId(AppendixIds);
                hrFeeReviewDTOs.setAppendixIdList(AppendixId);
            }

            if (hrFeeReviewDTOs.getDetailAppendixId() != null) {
                List<String> DetailAppendixIds = Arrays.asList(hrFeeReviewDTOs.getDetailAppendixId().split(","));
                AppendixId = this.hrStaffTurnPositiveRepository.selectAppendixId(DetailAppendixIds);
                hrFeeReviewDTOs.setDetailAppendixIdUrl(AppendixId);
            }
        }
        if (hrFeeReviewDTOs != null) {
            if (CollectionUtils.isNotEmpty(hrFeeReviewDTOs.getHrBillDTO())) {
                return hrFeeReviewDTOs;
            } else {
                throw new CommonException("账单不存在，请检查！");
            }
        } else {
            throw new CommonException("账单不存在，请检查！");
        }

    }


    @Override
    public HrFeeReviewDTO getHrFeeReview(String id, Integer type, String clientId) {
        log.info("Get HrFeeReview :{}", id);
        return null;
    }

    /**
     * 删除费用审核
     *
     * @param id
     */
    @Override
    public void deleteHrFeeReview(String id) {
        Optional.ofNullable(this.hrFeeReviewRepository.selectById(id))
            .ifPresent(hrFeeReview -> {
                this.hrFeeReviewRepository.deleteById(id);
                log.info("Delete HrFeeReview:{}", hrFeeReview);
            });
    }

    /**
     * 批量删除费用审核
     *
     * @param ids
     */
    @Override
    public void deleteHrFeeReview(List<String> ids) {
        log.info("Delete HrFeeReviews:{}", ids);
        this.hrFeeReviewRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询费用审核
     *
     * @param hrFeeReviewDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrFeeReviewDTO hrFeeReviewDTO, Long pageNumber, Long pageSize) {
        Page<HrFeeReview> page = new Page<>(pageNumber, pageSize);
        if (hrFeeReviewDTO.getField() != null) {
            if (hrFeeReviewDTO.getField().equals("client_name")) {
                hrFeeReviewDTO.setField("clientName");
            }
            if (hrFeeReviewDTO.getField().equals("real_name")) {
                hrFeeReviewDTO.setField("realName");
            }
            if (hrFeeReviewDTO.getField().equals("created_date")) {
                hrFeeReviewDTO.setField("createdDate");
            }

        }
        List<String> clientId = this.hrClientService.selectClientIdByUserId();
        if (CollectionUtils.isEmpty(hrFeeReviewDTO.getClientIdList())) {
            if (clientId != null) {
                hrFeeReviewDTO.setClientIdList(clientId);
            }
        }
        IPage<HrFeeReviewDTO> iPage = this.hrFeeReviewRepository.selectPages(page, hrFeeReviewDTO);
        for (HrFeeReviewDTO record : iPage.getRecords()) {
            if (StringUtils.isNotBlank(record.getBillId())){
                List<HrBill> hrBillList = hrBillRepository.selectBatchIds(Arrays.asList(record.getBillId().split(",")));
                List<String> clientIdList = hrBillList.stream().map(HrBill::getClientId).collect(Collectors.toList());
                record.setClientIdList(clientIdList);
            }
            List<HrAppendix> AppendixId = new ArrayList<>();
            if (record.getAppendixId() != null) {
                List<String> AppendixIds = Arrays.asList(record.getAppendixId().split(","));
                AppendixId = this.hrStaffTurnPositiveRepository.selectAppendixId(AppendixIds);
                record.setAppendixIdList(AppendixId);
            }

            if (record.getDetailAppendixId() != null) {
                List<String> DetailAppendixIds = Arrays.asList(record.getDetailAppendixId().split(","));
                AppendixId = this.hrStaffTurnPositiveRepository.selectAppendixId(DetailAppendixIds);
                record.setDetailAppendixIdUrl(AppendixId);
            }
            if (record.getSummaryAppendixId() != null) {
                List<String> DetailAppendixIds = Arrays.asList(record.getSummaryAppendixId().split(","));
                AppendixId = this.hrStaffTurnPositiveRepository.selectAppendixId(DetailAppendixIds);
                record.setSummaryAppendixIdUrl(AppendixId);
            }
            if (record.getDetailPdfAppendixId() != null) {
                List<HrAppendix>  appendixList = this.hrStaffTurnPositiveRepository.selectAppendixId(Arrays.asList(record.getDetailPdfAppendixId()));
                record.setDetailPdfAppendixUrl(appendixList);
            }
        }

        return iPage;
    }


    @Override
    public ResponseEntity getHrFeeReviewStaffEffectiveness(HrFeeReviewDTO hrFeeReviewDTO) {
        JSONObject jsonObject = new JSONObject();
        List<String> billIdList = hrFeeReviewDTO.getBillIdList();
        if (billIdList == null || billIdList.isEmpty()){
            throw new CommonException("请先选择账单！");
        }
        QueryWrapper<HrBill> qw = new QueryWrapper<>();
        qw.in("id",billIdList);
        qw.eq("bill_state", BillEnum.BillState.LOCKED.getKey());
        qw.eq("is_delete", 0);
        qw.eq("is_official", 1);
        qw.isNotNull("review_state");
        qw.notIn("review_state",BillEnum.FeeReviewState.AUDIT_REJECT.getKey(),BillEnum.FeeReviewState.ALREADY_CANCEL.getKey());
        List<HrBill> hrBill = this.hrBillRepository.selectList(qw);
        if (CollectionUtils.isNotEmpty(hrBill)){
            List<String> titleList = hrBill.stream().map(HrBill::getTitle).collect(Collectors.toList());
            List<String> toBeReviewed = hrBill.stream().filter(ls -> ls.getReviewState().equals(BillEnum.FeeReviewState.TO_BE_REVIEWED.getKey())).map(HrBill::getTitle).collect(Collectors.toList());
            List<String> approved = hrBill.stream().filter(ls -> ls.getReviewState().equals(BillEnum.FeeReviewState.TO_BE_REVIEWED.getKey())).map(HrBill::getTitle).collect(Collectors.toList());
            List<String> toBeCancel = hrBill.stream().filter(ls -> ls.getReviewState().equals(BillEnum.FeeReviewState.TO_BE_CANCEL.getKey())).map(HrBill::getTitle).collect(Collectors.toList());
            String message = "选择的账单中 " + String.join(",",titleList) + "已创建过结算单。其中："
                + (toBeReviewed.size() == 0 ? "" : String.join(",",toBeReviewed) + "处于待审核的状态" )
                + (approved.size() == 0 ? "" : String.join(",",approved) + "处于审核通过的状态" )
                + (toBeCancel.size() == 0 ? "" : String.join(",",toBeCancel) + "处于待确认作废的状态" );
            jsonObject.put("message",message);
            jsonObject.put("status", false);
        }else {
            jsonObject.put("message", "账单创建过程中请勿刷新或退出页面");
            jsonObject.put("status", true);
        }
        return ResponseUtil.buildSuccess(jsonObject);
    }


    @Override
    public Object getHrFeeReviewStaffEffReboot(HrFeeReviewDTO hrFeeReviewDTO) {
        QueryWrapper<HrFeeReview> qw = new QueryWrapper<>();
        List<String> status = new ArrayList<>();
        status.add("1");
        status.add("0");
        qw.eq("client_id", hrFeeReviewDTO.getClientId());
        qw.eq("pay_year", hrFeeReviewDTO.getPayYear());
        qw.eq("pay_monthly", hrFeeReviewDTO.getPayMonthly());
        qw.eq("is_delete", "0");
        qw.in("status", status);
        JSONObject jsonObject = new JSONObject();
        List<HrFeeReview> ListHrFeeReview = this.hrFeeReviewRepository.selectList(qw);

        if (CollectionUtils.isEmpty(ListHrFeeReview)) {
            jsonObject.put("status", true);

        } else {
            jsonObject.put("status", false);
        }

        return jsonObject;
    }

    @Override
    public void getHrFeeReviewLock(HrFeeReviewDTO hrFeeReviewDTO) {
        QueryWrapper<HrBill> qw = new QueryWrapper<>();
        List<String> clientIdLista = new ArrayList<>();
        List<String> clientIdList = this.hrFeeReviewRepository.selectChlientId(hrFeeReviewDTO.getClientId());
        clientIdLista.addAll(clientIdList);
        if (CollectionUtils.isNotEmpty(clientIdList)) {
            for (String s : clientIdList) {
                if (CollectionUtils.isNotEmpty(clientIdList)) {
                    List<String> clientIdLists = this.hrFeeReviewRepository.selectChlientId(s);
                    clientIdLista.addAll(clientIdLists);
                    if (CollectionUtils.isNotEmpty(clientIdLists)) {
                        for (String idList : clientIdLists) {
                            List<String> clientIdListss = this.hrFeeReviewRepository.selectChlientId(idList);
                            clientIdLista.addAll(clientIdListss);
                        }
                    }

                }
            }
        }
        clientIdLista.add(hrFeeReviewDTO.getClientId());
        qw.in("client_id", clientIdLista);
        qw.eq("pay_monthly", hrFeeReviewDTO.getPayMonthly());
        qw.eq("pay_year", hrFeeReviewDTO.getPayYear());
        qw.eq("is_official", 1);
        List<HrBill> hrFeeReview = this.hrBillRepository.selectList(qw);
        for (HrBill hrBill : hrFeeReview) {
            this.hrFeeReviewRepository.updateHrBill(hrBill.getId(), hrFeeReviewDTO.getType());
        }

    }

    /**
     * 作废结算单
     * @param hrFeeReviewDTO
     * @return
     */
    @Override
    public HrFeeReviewDTO launchCancelApply(HrFeeReviewDTO hrFeeReviewDTO) {
        HrFeeReview hrFeeReview = hrFeeReviewRepository.selectById(hrFeeReviewDTO.getId());
        hrFeeReview.setStatus(BillEnum.FeeReviewState.TO_BE_CANCEL.getKey());
        hrFeeReview.setCancelReason(hrFeeReviewDTO.getCancelReason());
        hrFeeReview.setLastModifiedDate(LocalDateTime.now());
        hrFeeReviewRepository.updateById(hrFeeReview);
        List<String> billIds = Arrays.asList(hrFeeReview.getBillId().split(","));
        hrBillRepository.updateBillReviewState(billIds,BillEnum.FeeReviewState.TO_BE_CANCEL.getKey());
        return hrFeeReviewMapper.toDto(hrFeeReview);
    }

    /**
     * 作废审核
     * @param hrFeeReviewDTO
     * @return
     */
    @Override
    public HrFeeReviewDTO confirmCancelApply(HrFeeReviewDTO hrFeeReviewDTO) {
        HrFeeReview hrFeeReview = hrFeeReviewRepository.selectById(hrFeeReviewDTO.getId());
        hrFeeReview.setStatus(hrFeeReviewDTO.getOpt() ? BillEnum.FeeReviewState.ALREADY_CANCEL.getKey() : BillEnum.FeeReviewState.APPROVED.getKey());
        hrFeeReview.setCancelRemark(hrFeeReviewDTO.getCancelRemark());
        hrFeeReview.setLastModifiedDate(LocalDateTime.now());
        hrFeeReviewRepository.updateById(hrFeeReview);
        if (hrFeeReviewDTO.getOpt()){
            //结算单包含的相关账单解除锁定
            List<String> billIds = Arrays.asList(hrFeeReview.getBillId().split(","));
            hrBillRepository.updateReviewStateAndBillState(billIds,BillEnum.FeeReviewState.ALREADY_CANCEL.getKey(), BillEnum.BillState.NOT_LOCKED.getKey());
            // fix  Bug用户2024,6月份有多条结算单， 开票申请，到账记录.     将其中一条结算单作废， 用户2024 6月份到账记录都会删除
            // 未选择开票的结算单作废到账记录删除
            QueryWrapper<HrArrivalRecord> qw = new QueryWrapper<>();
            qw.eq("client_id",hrFeeReview.getClientId());
            qw.eq("pay_year",hrFeeReview.getPayYear());
            qw.eq("pay_monthly",hrFeeReview.getPayMonthly());
            qw.eq("fee_review_id", hrFeeReview.getId());
            List<HrArrivalRecord> list = hrArrivalRecordService.list(qw);
            List<String> collect = list.stream().map(HrArrivalRecord::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)){
                hrArrivalRecordService.deleteHrArrivalRecord(collect);
            }

            // 根据开票id删除到账记录
            List<HrBillInvoiceDTO> hrBillInvoiceDTOS = hrBillInvoiceRepository.findByReviewId(Collections.singletonList(hrFeeReview.getId()), null);
            List<String> arrivalRecordIdsByInvoiceId = hrArrivalRecordService.list(new LambdaQueryWrapper<>(HrArrivalRecord.class)
                    .in(HrArrivalRecord::getBillInvoiceId, hrBillInvoiceDTOS.stream().map(HrBillInvoiceDTO::getId).collect(Collectors.toList())))
                .stream().map(HrArrivalRecord::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(arrivalRecordIdsByInvoiceId)) {
                hrArrivalRecordService.deleteHrArrivalRecord(arrivalRecordIdsByInvoiceId);
            }

            //与该账单相关联的申请中的开票、报销均会结束流程，申请成功的开票、报销会直接作废
            hrBillInvoiceRepository.updateApproveStatus(hrFeeReview.getId(), BillInvoiceApproveEnums.ALREADY_CANCEL.getKey(),BillInvoiceApproveEnums.InvoiceLockState.LOCKED.getKey());
            hrBillReimbursementApplyRepository.updateApproveStatus(hrFeeReview.getId(), BillReimbApproveEnums.ALREADY_CANCEL.getKey(),BillInvoiceApproveEnums.InvoiceLockState.LOCKED.getKey());

            // Jigsaw - 2025/8/11 ：删除手动新增的报销申请
            List<String> applyIds = hrBillReimbursementApplyRepository.selectApplyIdByFeeReviewId(hrFeeReview.getId());
            if (CollectionUtils.isNotEmpty(applyIds)) {
                applyIds.forEach(applyId -> {
                    hrBillReimbursementApplyRepository.updateApproveStatusByApplyId(applyId, BillReimbApproveEnums.ALREADY_CANCEL.getKey(), BillInvoiceApproveEnums.InvoiceLockState.LOCKED.getKey());
                });
            }

        } else {
            // 拒绝作废
            // 将薪酬单 保障单的审核状态恢复为 审核通过  [薪酬单的审核状态 决定到账记录是否展示]
            List<String> billIds = Arrays.asList(hrFeeReview.getBillId().split(","));
            hrBillRepository.updateBillReviewState(billIds,BillEnum.FeeReviewState.APPROVED.getKey());
        }
        return hrFeeReviewMapper.toDto(hrFeeReview);
    }

    /**
     * 查询所属账单
     * @param hrFeeReviewDTO
     * @return
     */
    @Override
    public List<HrBillDTO> queryBillInfo(HrFeeReviewDTO hrFeeReviewDTO) {
        return hrBillRepository.queryBillInfo(hrFeeReviewDTO);
    }

    @Override
    public List<HrClientDTO> queryClientInfo(HrFeeReviewDTO hrFeeReviewDTO) {
        List<String> clientIdList = hrClientService.querySubordinateClient(hrFeeReviewDTO.getClientId());
        if (clientIdList == null || clientIdList.isEmpty()){
            throw new CommonException("未查询到客户信息！");
        }
        HrClient rootParentClient = hrClientRepository.getRootParentClient(hrFeeReviewDTO.getClientId());
        List<HrClientDTO> hrClientDTOList = hrClientRepository.findHrClientBatch(clientIdList);
        hrClientDTOList.forEach(hrClient ->{
            if (rootParentClient.getId().equals(SpecialBillClient.HAIER.getKey())){
                hrClient.setReviewFlag(1);
            }
        });
        return hrClientDTOList;
    }

    /**
     * 生成结算单信息
     * @param hrFeeReviewDTO
     * @return
     */
    @Override
    public HrSettleAccountDTO generateSettlementDocumentInfo(HrFeeReviewDTO hrFeeReviewDTO) {
        List<HrBill> billList = hrBillRepository.selectBatchIds(hrFeeReviewDTO.getBillIdList());
        if (CollectionUtils.isEmpty(billList)) {
            throw new CommonException("没有可用的账单创建结算单！");
        }
        List<String> clientId = billList.stream().map(HrBill::getClientId).collect(Collectors.toList());
        if (clientId == null || clientId.isEmpty()){
            throw new CommonException("未查询到客户信息！");
        }
        List<HrClientDTO> hrClientDTOList = hrClientRepository.findHrClientBatch(clientId);

        HrClientDTO hrClientDTO = hrClientRepository.findHrClientBatch(Collections.singletonList(hrFeeReviewDTO.getClientId())).get(0);

        HrSettleAccountDTO hrSettleAccountDTO = new HrSettleAccountDTO();
        hrSettleAccountDTO.setSuperiorUnit(hrClientDTO.getSuperiorUnit());
        hrSettleAccountDTO.setClientName(hrClientDTO.getClientName());
        hrSettleAccountDTO.setUnitNumber(hrClientDTO.getUnitNumber());
        hrSettleAccountDTO.setFeeReviewDate(hrFeeReviewDTO.getFeeReviewDate());
        hrSettleAccountDTO.setBillIdList(hrFeeReviewDTO.getBillIdList());
        hrSettleAccountDTO.setClientId(hrFeeReviewDTO.getClientId());
        hrSettleAccountDTO.setHrBillDTO(hrBillMapper.toDto(billList));
        hrSettleAccountDTO.setHrClientDTO(hrClientDTOList);
        hrSettleAccountDTO.setReviewChoiceDTOList(hrFeeReviewDTO.getReviewChoiceDTOList());
        // 判断是否为中石化账单
        if (billList.get(0).getBillType().equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey())) {
            hrSettleAccountDTO.setBillType(3);
            List<HrBillDetailDTO> listByBillIdBatch = hrBillDetailRepository.getListByBillIdBatch(hrFeeReviewDTO.getBillIdList(), 1);
            Map<String, List<HrBillDetailDTO>> listMap = listByBillIdBatch.stream().collect(Collectors.groupingBy(HrBillDetailDTO::getProjectName));
            List<HrBillDetailDTO> hrBillDetailDTOS = new ArrayList<>();
            for (String projectName : listMap.keySet()) {
                List<HrBillDetailDTO> hrBillDetailDTOList = listMap.get(projectName);
                if (CollectionUtils.isEmpty(hrBillDetailDTOList)) {
                    continue;
                }
                List<String> billDetailIds = hrBillDetailDTOList.stream().map(HrBillDetailDTO::getId).collect(Collectors.toList());
                HrBillDetailDTO billDetailDTO = new HrBillDetailDTO();
                HrStaffSalaryDetailDTO staffSalaryDetailDTO = new HrStaffSalaryDetailDTO();
                hrBillDetailService.assignmentStaffSalaryDetail(staffSalaryDetailDTO, billDetailIds);
                int peopleNum = hrBillDetailDTOList.stream().filter(lst -> lst.getPeopleNum() != null).mapToInt(HrBillDetailDTO::getPeopleNum).sum();
                BigDecimal total = hrBillDetailDTOList.stream().filter(ls -> ls.getTotal() != null).map(HrBillDetailDTO::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal totalOccurrence = hrBillDetailDTOList.stream().filter(ls -> ls.getTotalOccurrence() != null).map(HrBillDetailDTO::getTotalOccurrence).reduce(BigDecimal.ZERO, BigDecimal::add);
                BeanUtils.copyProperties(hrBillDetailDTOList.get(0), billDetailDTO);
                billDetailDTO.setPeopleNum(peopleNum)
                    .setPayYear(hrFeeReviewDTO.getPayYear())
                    .setPayMonthly(hrFeeReviewDTO.getPayMonthly())
                    .setTotal(total).setTotalOccurrence(totalOccurrence)
                    .setReason(String.join(",", billDetailIds));
                this.setDynamicItemsValue(billDetailDTO, staffSalaryDetailDTO);
                hrBillDetailDTOS.add(billDetailDTO);
            }
            hrSettleAccountDTO.setHrBillDetailDTO(hrBillDetailDTOS);
            List<HrBillTotalDTO> hrBillTotalDTOList = hrBillTotalRepository.getBillTotalByBillId(hrFeeReviewDTO.getBillIdList());
            hrSettleAccountDTO.setHrBillTotalDTOList(hrBillTotalDTOList);
        }
        else {
            HrClient rootParentClient = hrClientRepository.getRootParentClient(hrFeeReviewDTO.getClientId());
            if (!rootParentClient.getId().equals(SpecialBillClient.HAIER.getKey())) {
                hrSettleAccountDTO.setBillType(1);
                List<HrBillDetailDTO> listByBillIdBatch = hrBillDetailRepository.getListBatch(hrFeeReviewDTO, 1);
                BigDecimal serviceFeeTotal = BigDecimal.ZERO;
                BigDecimal taxationFeeTotal = BigDecimal.ZERO;
                BigDecimal total = BigDecimal.ZERO;
                BigDecimal personalTaxTotal = BigDecimal.ZERO;
                BigDecimal chargeTotal = BigDecimal.ZERO;
                BigDecimal refundTotal = BigDecimal.ZERO;

                // 0普通单位 1外包单位
                int clientType = 0;
                HrProtocol hrProtocol = hrBillService.checkProtocol(hrFeeReviewDTO.getClientId());
                if (hrProtocol.getAgreementType() != null && hrProtocol.getAgreementType() == 2) {
                    clientType = 1;
                }
                hrSettleAccountDTO.setClientType(clientType);
                Map<String, List<HrBillDetailDTO>> listMap = listByBillIdBatch.stream().collect(Collectors.groupingBy(HrBillDetailDTO::getCertificateNum));

                List<HrBillDetailDTO> hrBillDetailResultList = new ArrayList<>();
                List<HrBill> hrBillList = billList.stream().filter(ls ->
                    ls.getBillType().equals(BillEnum.BillType.SALARY_BILL.getKey()) || ls.getBillType().equals(BillEnum.BillType.OTHER_BILL.getKey())).collect(Collectors.toList());

                // 只有保障账单
                if (CollectionUtils.isEmpty(hrBillList)) {
                    for (String certificateNum : listMap.keySet()) {
                        List<HrBillDetailDTO> billDetailDTOList = listMap.get(certificateNum);
                        if (CollectionUtils.isNotEmpty(billDetailDTOList)) {
                            HrBillDetailDTO billDetailDTO = new HrBillDetailDTO();
                            billDetailDTO.setPayYear(hrFeeReviewDTO.getPayYear());
                            billDetailDTO.setPayMonthly(hrFeeReviewDTO.getPayMonthly());
                            HrBillDetailDTO hrBillDetailDTO = billDetailDTOList.get(0);
                            BeanUtils.copyProperties(hrBillDetailDTO, billDetailDTO);
                            // 计算保障账单公积金金额
                            BigDecimal unitAccumulationFund = CalculateUtils.decimalAddition(billDetailDTO.getUnitAccumulationFund(), billDetailDTO.getUnitAccumulationFundMakeUp());
                            billDetailDTO.setUnitAccumulationFund(unitAccumulationFund);
                            BigDecimal personalAccumulationFund = CalculateUtils.decimalAddition(billDetailDTO.getPersonalAccumulationFund(), billDetailDTO.getPersonalAccumulationFundMakeUp());
                            billDetailDTO.setPersonalAccumulationFund(personalAccumulationFund);
                            billDetailDTO.setUnitAccumulationFundMakeUp(BigDecimal.ZERO).setPersonalAccumulationFundMakeUp(BigDecimal.ZERO);
                            List<String> billDetailIds = billDetailDTOList.stream().map(HrBillDetailDTO::getId).collect(Collectors.toList());
                            billDetailDTO.setReason(String.join(",", billDetailIds));
                            // 计算费用合计 = 保障账单总金额
                            billDetailDTO.setTotal(hrBillDetailDTO.getTotal());

                            // 计算服务费
                            HrBillDTO hrBillDTO = new HrBillDTO();
                            hrBillDTO.setPayYear(hrFeeReviewDTO.getPayYear())
                                .setPayMonthly(hrFeeReviewDTO.getPayMonthly())
                                .setClientId(hrBillDetailDTO.getClientId());
                            hrBillService.initServiceFee(hrBillDTO, billDetailDTO, clientType);
                            if (clientType == 0) {
                                billDetailDTO.setServiceFee(hrBillDTO.getMonthlyServiceFee());
                            }
                            // 外包单位计算税费
                            else {
                                billDetailDTO.setServiceFee(hrBillDTO.getServiceCharge());
                                billDetailDTO.setTaxationFee(hrBillDTO.getMonthlyServiceFee());
                            }
                            // 计算总金额 = 费用合计 + 服务费
                            billDetailDTO.setTotal(CalculateUtils.decimalListAddition(billDetailDTO.getTotal(), billDetailDTO.getServiceFee(), billDetailDTO.getTaxationFee()));
                            serviceFeeTotal = CalculateUtils.decimalListAddition(billDetailDTO.getServiceFee(), serviceFeeTotal);
                            taxationFeeTotal = CalculateUtils.decimalListAddition(billDetailDTO.getTaxationFee(), taxationFeeTotal);
                            total = CalculateUtils.decimalListAddition(billDetailDTO.getTotal(), total);
                            hrBillDetailResultList.add(billDetailDTO);
                        }
                    }
                } else {
                    // 判断客户是否是海尔公司
                    List<HrBill> salaryList = hrBillList.stream().filter(lst -> lst.getBillType().equals(BillEnum.BillType.SALARY_BILL.getKey())).collect(Collectors.toList());
                    // 社会治理客户选择的薪酬账单账单用途
                    if (rootParentClient.getId().equals(SpecialBillClient.SOCIAL_GOVERNANCE.getKey()) && CollectionUtils.isNotEmpty(salaryList)) {
                        List<Integer> billPurposeList = salaryList.stream().map(HrBill::getBillPurpose).distinct().collect(Collectors.toList());
                        if (billPurposeList.size() == 1) {
                            Integer billPurpose = billPurposeList.get(0);
                            hrSettleAccountDTO.setSocietyFlag(billPurpose == 1 ? 1 : 2);
                        } else {
                            throw new CommonException("选择的薪酬账单中存在多个账单用途，不可创建结算单");
                        }
                    }
                    if (salaryList != null && !salaryList.isEmpty()){
                        List<String> billIds = salaryList.stream().map(HrBill::getId).collect(Collectors.toList());
                        //统计薪酬账单的计税方式
                        int count = hrBillDetailRepository.countTaxCalculationMethod(billIds);
                        if (count > 1){
                            throw new CommonException("选择的薪酬账单中存在多个计税方式，不可创建结算单");
                        }
                    }
                    for (String certificateNum : listMap.keySet()) {
                        List<HrBillDetailDTO> billDetailDTOList = listMap.get(certificateNum);
                        if (CollectionUtils.isNotEmpty(billDetailDTOList)) {
                            HrBillDetailDTO billDetailDTO = new HrBillDetailDTO();
                            billDetailDTO.setPayYear(hrFeeReviewDTO.getPayYear());
                            billDetailDTO.setPayMonthly(hrFeeReviewDTO.getPayMonthly());
                            HrStaffSalaryDetailDTO staffSalaryDetailDTO = new HrStaffSalaryDetailDTO();
                            // 赋值薪酬/其他账单的动态费用信息
                            List<String> billDetailIds = billDetailDTOList.stream().map(HrBillDetailDTO::getId).collect(Collectors.toList());
                            hrBillDetailService.assignmentStaffSalaryDetail(staffSalaryDetailDTO, billDetailIds);
                            List<HrBillDetailDTO> collect = billDetailDTOList.stream().filter(ls -> ls.getBillType().equals(BillEnum.BillType.SECURITY_BILL.getKey())).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(collect)) {
                                BeanUtils.copyProperties(collect.get(0), billDetailDTO);
                                // 计算保障账单公积金金额
                                BigDecimal unitAccumulationFund = CalculateUtils.decimalAddition(billDetailDTO.getUnitAccumulationFund() == null ? BigDecimal.ZERO : billDetailDTO.getUnitAccumulationFund(),
                                    billDetailDTO.getUnitAccumulationFundMakeUp() == null ? BigDecimal.ZERO : billDetailDTO.getUnitAccumulationFundMakeUp());
                                billDetailDTO.setUnitAccumulationFund(unitAccumulationFund);
                                BigDecimal personalAccumulationFund = CalculateUtils.decimalAddition(billDetailDTO.getPersonalAccumulationFund() == null ? BigDecimal.ZERO : billDetailDTO.getPersonalAccumulationFund(),
                                    billDetailDTO.getPersonalAccumulationFundMakeUp() == null ? BigDecimal.ZERO : billDetailDTO.getPersonalAccumulationFundMakeUp());
                                billDetailDTO.setPersonalAccumulationFund(personalAccumulationFund);
                                billDetailDTO.setUnitAccumulationFundMakeUp(BigDecimal.ZERO).setPersonalAccumulationFundMakeUp(BigDecimal.ZERO);
                            } else {
                                List<HrBillDetailDTO> salaryBillCollect = billDetailDTOList.stream().filter(ls -> ls.getBillType().equals(BillEnum.BillType.SALARY_BILL.getKey())).collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(salaryBillCollect)) {
                                    HrBillDetailDTO detailDTO = salaryBillCollect.get(0);
                                    billDetailDTO.setId(detailDTO.getId())
                                        .setStaffId(detailDTO.getStaffId())
                                        .setBillId(detailDTO.getBillId())
                                        .setPayYear(detailDTO.getPayYear())
                                        .setPayMonthly(detailDTO.getPayMonthly())
                                        .setSystemNum(detailDTO.getSystemNum())
                                        .setName(detailDTO.getName())
                                        .setCertificateNum(detailDTO.getCertificateNum())
                                        .setPhone(detailDTO.getPhone())
                                        .setSocialSecurityArea(detailDTO.getSocialSecurityArea())
                                        .setStaffStatus(detailDTO.getStaffStatus())
                                        .setPersonnelType(detailDTO.getPersonnelType())
                                        .setIzInsured(detailDTO.getIzInsured())
                                        .setSortValue(detailDTO.getSortValue())
                                        .setUnitSubtotal(BigDecimal.ZERO)
                                        .setUnitAccumulationFund(BigDecimal.ZERO)
                                        .setClientId(detailDTO.getClientId())
                                        .setClientName(detailDTO.getClientName())
                                        .setParentClientName(detailDTO.getParentClientName())
                                        .setParentUnitNumber(detailDTO.getParentUnitNumber())
                                    ;
                                } else {
                                    List<HrBillDetailDTO> otherBillCollect = billDetailDTOList.stream().filter(ls -> ls.getBillType().equals(BillEnum.BillType.OTHER_BILL.getKey())).collect(Collectors.toList());
                                    if (CollectionUtils.isNotEmpty(otherBillCollect)) {
                                        BeanUtils.copyProperties(otherBillCollect.get(0), billDetailDTO);
                                    }
                                }
                            }

                            billDetailDTO.setReason(String.join(",", billDetailIds));
                            // 应发工资以及税前应发不计算全年一次性奖金
                            List<HrBillDetailDTO> hrBillDetailList = billDetailDTOList.stream().filter(ls -> ls.getBillType().equals(BillEnum.BillType.SALARY_BILL.getKey()) && ls.getTaxCalculationMethod() != 1).collect(Collectors.toList());
                            // 计算应发工资
                            BigDecimal salary = hrBillDetailList.stream().filter(ls -> ls.getSalary() != null).map(HrBillDetailDTO::getSalary).reduce(BigDecimal.ZERO, BigDecimal::add);
                            billDetailDTO.setSalary(salary);
                            // 计算税前应发
                            BigDecimal preTaxSalary = hrBillDetailList.stream().filter(ls -> ls.getPreTaxSalary() != null).map(HrBillDetailDTO::getPreTaxSalary).reduce(BigDecimal.ZERO, BigDecimal::add);
                            billDetailDTO.setPreTaxSalary(preTaxSalary);

                            List<HrBillDetailDTO> hrBillDetailDTOList = billDetailDTOList.stream().filter(ls -> ls.getBillType().equals(BillEnum.BillType.SALARY_BILL.getKey())).collect(Collectors.toList());
                            // 计算个税
                            BigDecimal personalTax = hrBillDetailDTOList.stream().filter(ls -> ls.getPersonalTax() != null).map(HrBillDetailDTO::getPersonalTax).reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal personalTaxMakeUp = hrBillDetailDTOList.stream().filter(ls -> ls.getPersonalTaxMakeUp() != null).map(HrBillDetailDTO::getPersonalTaxMakeUp).reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal bigDecimalPersonalTax = CalculateUtils.decimalAddition(personalTax, personalTaxMakeUp);
                            billDetailDTO.setPersonalTax(bigDecimalPersonalTax);

                            // 计算实发工资
                            BigDecimal realSalary = hrBillDetailDTOList.stream().filter(ls -> ls.getRealSalary() != null).map(HrBillDetailDTO::getRealSalary).reduce(BigDecimal.ZERO, BigDecimal::add);
                            billDetailDTO.setRealSalary(realSalary);
                            //总金额 = 保障账单总计 + 薪酬账单总计 + 服务费
                            BigDecimal totalFee = billDetailDTOList.stream().filter(lst -> !lst.getBillType().equals(BillEnum.BillType.OTHER_BILL.getKey()) && lst.getTotal() != null).map(HrBillDetailDTO::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
                            billDetailDTO.setTotal(totalFee);
                            // 计算服务费
                            List<HrBillDetailItemsDTO> serviceFeeItems = staffSalaryDetailDTO.getServiceFeeItems();
                            // 海尔集团并且费用项存在服务费
                            if (rootParentClient.getId().equals(SpecialBillClient.HAIER.getKey()) && CollectionUtils.isNotEmpty(serviceFeeItems)) {
                                // 结算单服务费 = 薪酬费用项服务费总金额
                                BigDecimal serviceFee = serviceFeeItems.stream().map(HrBillDetailItemsDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                                billDetailDTO.setServiceFee(serviceFee);
                            } else {
                                HrBillDTO hrBillDTO = new HrBillDTO();
                                hrBillDTO.setPayYear(hrFeeReviewDTO.getPayYear())
                                    .setPayMonthly(hrFeeReviewDTO.getPayMonthly())
                                    .setClientId(billDetailDTOList.get(0).getClientId());
                                hrBillService.initServiceFee(hrBillDTO, billDetailDTO, clientType);
                                if (clientType == 0) {
                                    billDetailDTO.setServiceFee(hrBillDTO.getMonthlyServiceFee());
                                }
                                // 外包单位计算税费
                                else {
                                    billDetailDTO.setServiceFee(hrBillDTO.getServiceCharge());
                                    billDetailDTO.setTaxationFee(hrBillDTO.getMonthlyServiceFee());
                                }
                            }
                            // 计算总金额 = 费用合计 + 服务费 + 税费
                            billDetailDTO.setTotal(CalculateUtils.decimalListAddition(billDetailDTO.getTotal(), billDetailDTO.getServiceFee(), billDetailDTO.getTaxationFee()));
                            // 动态项
                            List<HrBillDetailItemsDTO> chargeItems = staffSalaryDetailDTO.getChargeItems();
                            List<HrBillDetailItemsDTO> refundItems = staffSalaryDetailDTO.getRefundItems();
                            billDetailDTO.setWageIncrease(staffSalaryDetailDTO.getWageIncrease())
                                .setWageDeduction(staffSalaryDetailDTO.getWageDeduction())
                                .setOtherExpenses(staffSalaryDetailDTO.getOtherExpenses())
                                .setChargeItems(chargeItems)
                                .setRefundItems(refundItems);
                            if (CollectionUtils.isNotEmpty(chargeItems)) {
                                BigDecimal reduce = chargeItems.stream().filter(lst -> lst.getAmount() != null).map(HrBillDetailItemsDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                                chargeTotal = CalculateUtils.decimalListAddition(reduce, chargeTotal);
                                billDetailDTO.setTotal(CalculateUtils.decimalListAddition(billDetailDTO.getTotal(), reduce));
                            }
                            if (CollectionUtils.isNotEmpty(refundItems)) {
                                BigDecimal reduce = refundItems.stream().filter(lst -> lst.getAmount() != null).map(HrBillDetailItemsDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                                refundTotal = CalculateUtils.decimalListAddition(reduce, refundTotal);
                                billDetailDTO.setTotal(CalculateUtils.decimalSubtraction(billDetailDTO.getTotal(), reduce));
                            }
                            serviceFeeTotal = CalculateUtils.decimalListAddition(billDetailDTO.getServiceFee(), serviceFeeTotal);
                            taxationFeeTotal = CalculateUtils.decimalListAddition(billDetailDTO.getTaxationFee(), taxationFeeTotal);
                            total = CalculateUtils.decimalListAddition(billDetailDTO.getTotal(), total);
                            personalTaxTotal = CalculateUtils.decimalListAddition(billDetailDTO.getPersonalTax(), personalTaxTotal);
                            hrBillDetailResultList.add(billDetailDTO);
                        }
                    }
                }
                hrSettleAccountDTO.setHrBillDetailDTO(hrBillDetailResultList);
                List<String> paymentDate = hrBillDetailResultList.stream().map(HrBillDetailDTO::getPaymentDate).distinct().collect(Collectors.toList());
                hrSettleAccountDTO.setPaymentDate(String.join(",", paymentDate));
                // 处理其他账单-客户
                List<String> collect = billList.stream().filter(ls -> ls.getBillType().equals(BillEnum.BillType.OTHER_BILL.getKey()) && ls.getOtherBillFlag() != null && ls.getOtherBillFlag() == 1).map(HrBill::getId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    List<HrBillDetailDTO> hrBillDetailDTOList = hrBillDetailRepository.getBillDetailByIds(collect, null);
                    for (HrBillDetailDTO hrBillDetailDTO : hrBillDetailDTOList) {
                        List<HrBillDetailItemsDTO> hrBillDetailItemsList = hrBillDetailDTO.getHrBillDetailItemsList();
                        List<HrBillDetailItemsDTO> chargeItems = hrBillDetailItemsList.stream().filter(lst -> lst.getExpenseType().equals(DynamicFeeTypesEnum.CHARGE_ITEM.getKey())).collect(Collectors.toList());
                        List<HrBillDetailItemsDTO> refundItems = hrBillDetailItemsList.stream().filter(lst -> lst.getExpenseType().equals(DynamicFeeTypesEnum.REFUND_ITEM.getKey())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(chargeItems)) {
                            BigDecimal reduce = chargeItems.stream().filter(lst -> lst.getAmount() != null).map(HrBillDetailItemsDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                            total = CalculateUtils.decimalListAddition(total, reduce);
                            chargeTotal = CalculateUtils.decimalListAddition(reduce, chargeTotal);
                        }
                        if (CollectionUtils.isNotEmpty(refundItems)) {
                            BigDecimal reduce = refundItems.stream().filter(lst -> lst.getAmount() != null).map(HrBillDetailItemsDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                            total = CalculateUtils.decimalSubtraction(total, reduce);
                            refundTotal = CalculateUtils.decimalListAddition(reduce, refundTotal);
                        }
                    }
                }
                List<String> billIds = billList.stream().filter(ls -> ls.getBillType().equals(BillEnum.BillType.SECURITY_BILL.getKey())).map(HrBill::getId).collect(Collectors.toList());
                HrBillTotalDTO hrBillTotalDTO = null;
                if (CollectionUtils.isNotEmpty(billIds)) {
                    hrBillTotalDTO = hrBillTotalRepository.getBillTotalByBatchBill(billIds);
                    if (hrBillTotalDTO != null) {
                        BigDecimal unitAccumulationFundTotal = CalculateUtils.decimalAddition(hrBillTotalDTO.getUnitAccumulationFundTotal() == null ? BigDecimal.ZERO : hrBillTotalDTO.getUnitAccumulationFundTotal(),
                            hrBillTotalDTO.getUnitAccumulationFundMakeUpTotal() == null ? BigDecimal.ZERO : hrBillTotalDTO.getUnitAccumulationFundMakeUpTotal());
                        hrBillTotalDTO.setUnitAccumulationFundTotal(unitAccumulationFundTotal);

                        BigDecimal personalAccumulationFundTotal = CalculateUtils.decimalAddition(hrBillTotalDTO.getPersonalAccumulationFundTotal() == null ? BigDecimal.ZERO : hrBillTotalDTO.getPersonalAccumulationFundTotal(),
                            hrBillTotalDTO.getPersonalAccumulationFundMakeUpTotal() == null ? BigDecimal.ZERO : hrBillTotalDTO.getPersonalAccumulationFundMakeUpTotal());
                        hrBillTotalDTO.setPersonalAccumulationFundTotal(personalAccumulationFundTotal);
                        hrBillTotalDTO.setUnitAccumulationFundMakeUpTotal(BigDecimal.ZERO).setPersonalAccumulationFundMakeUpTotal(BigDecimal.ZERO);
                    }
                }

                List<String> billIdList = billList.stream().filter(ls -> ls.getBillType().equals(BillEnum.BillType.SALARY_BILL.getKey())).map(HrBill::getId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(billIdList)) {
                    HrBillTotalDTO hrBillTotal = hrBillTotalRepository.getBillTotalBySalaryBill(billIdList);
                    if (hrBillTotal != null) {
                        if (hrBillTotalDTO == null) {
                            hrBillTotalDTO = hrBillTotal;
                        } else {
                            hrBillTotalDTO.setPersonalTaxTotal(hrBillTotal.getPersonalTaxTotal());
                            hrBillTotalDTO.setRealSalaryTotal(hrBillTotal.getRealSalaryTotal());
                            hrBillTotalDTO.setOtherFeeTotal(hrBillTotal.getOtherFeeTotal());
                        }
                    }
                }
                if (hrBillTotalDTO == null) {
                    hrBillTotalDTO = new HrBillTotalDTO();
                }
                int staffNum = 0;
                if (CollectionUtils.isNotEmpty(hrBillDetailResultList)) {
                    List<String> list = hrBillDetailResultList.stream().map(HrBillDetailDTO::getCertificateNum).distinct().collect(Collectors.toList());
                    staffNum = list.size();
                }
                hrBillTotalDTO.setStaffNum(staffNum);
                hrBillTotalDTO.setServiceFeeTotal(serviceFeeTotal);
                hrBillTotalDTO.setTaxationFeeTotal(taxationFeeTotal);
                hrBillTotalDTO.setTotal(total);
                hrBillTotalDTO.setPersonalTaxTotal(personalTaxTotal);
                hrBillTotalDTO.setChargeTotal(chargeTotal);
                hrBillTotalDTO.setRefundTotal(refundTotal);
                hrSettleAccountDTO.setHrBillTotalDTO(hrBillTotalDTO);
            } else {
                hrSettleAccountDTO.setBillType(2);
                if (hrFeeReviewDTO.getHrBillDetailDTO() != null && !hrFeeReviewDTO.getHrBillDetailDTO().isEmpty()){
                    hrSettleAccountDTO.setHrBillDetailDTO(hrFeeReviewDTO.getHrBillDetailDTO());
                }
                if (hrFeeReviewDTO.getHrBillTotalDTO() != null && !hrFeeReviewDTO.getHrBillTotalDTO().isEmpty()){
                    hrSettleAccountDTO.setHrBillTotalDTO(hrFeeReviewDTO.getHrBillTotalDTO().get(0));
                }
            }
        }
        return hrSettleAccountDTO;
    }

    /**
     * 赋值动态费用项
     *
     * @param billDetailDTO
     * @param staffSalaryDetailDTO
     */
    private void setDynamicItemsValue(HrBillDetailDTO billDetailDTO, HrStaffSalaryDetailDTO staffSalaryDetailDTO) {
        if (CollectionUtils.isNotEmpty(staffSalaryDetailDTO.getEmployeeCompensationList())) {
            List<HrBillDetailItemsDTO> list = this.handleDynamicItemsByAmount(staffSalaryDetailDTO.getEmployeeCompensationList());
            billDetailDTO.setEmployeeCompensationList(list);
        }
        if (CollectionUtils.isNotEmpty(staffSalaryDetailDTO.getUnitInsuranceList())) {
            List<HrBillDetailItemsDTO> list = this.handleDynamicItemsByAmount(staffSalaryDetailDTO.getUnitInsuranceList());
            billDetailDTO.setUnitInsuranceList(list);
        }
        if (CollectionUtils.isNotEmpty(staffSalaryDetailDTO.getUnitHousingFundList())){
            List<HrBillDetailItemsDTO> list = this.handleDynamicItemsByAmount(staffSalaryDetailDTO.getUnitHousingFundList());
            billDetailDTO.setUnitHousingFundList(list);
        }
        if (CollectionUtils.isNotEmpty(staffSalaryDetailDTO.getOtherExpenseList())){
            List<HrBillDetailItemsDTO> list = this.handleDynamicItemsByAmount(staffSalaryDetailDTO.getOtherExpenseList());
            billDetailDTO.setOtherExpenseList(list);
        }
        if (CollectionUtils.isNotEmpty(staffSalaryDetailDTO.getReimbursementExpenseList())){
            List<HrBillDetailItemsDTO> list = this.handleDynamicItemsByAmount(staffSalaryDetailDTO.getReimbursementExpenseList());
            billDetailDTO.setReimbursementExpenseList(list);
        }
        if (CollectionUtils.isNotEmpty(staffSalaryDetailDTO.getManagementExpenseList())){
            List<HrBillDetailItemsDTO> list = this.handleDynamicItemsByAmount(staffSalaryDetailDTO.getManagementExpenseList());
            billDetailDTO.setManagementExpenseList(list);
        }
        if (CollectionUtils.isNotEmpty(staffSalaryDetailDTO.getTaxesList())){
            List<HrBillDetailItemsDTO> list = this.handleDynamicItemsByAmount(staffSalaryDetailDTO.getTaxesList());
            billDetailDTO.setTaxesList(list);
        }
        if (CollectionUtils.isNotEmpty(staffSalaryDetailDTO.getIndividualHousingFundList())){
            List<HrBillDetailItemsDTO> list = this.handleDynamicItemsByAmount(staffSalaryDetailDTO.getIndividualHousingFundList());
            billDetailDTO.setIndividualHousingFundList(list);
        }
        if (CollectionUtils.isNotEmpty(staffSalaryDetailDTO.getIndividualIncomeTaxList())){
            List<HrBillDetailItemsDTO> list = this.handleDynamicItemsByAmount(staffSalaryDetailDTO.getIndividualIncomeTaxList());
            billDetailDTO.setIndividualIncomeTaxList(list);
        }
        if (CollectionUtils.isNotEmpty(staffSalaryDetailDTO.getIndividualInsuranceList())){
            List<HrBillDetailItemsDTO> list = this.handleDynamicItemsByAmount(staffSalaryDetailDTO.getIndividualInsuranceList());
            billDetailDTO.setIndividualInsuranceList(list);
        }
        if (CollectionUtils.isNotEmpty(staffSalaryDetailDTO.getTotalExpensesList())){
            List<HrBillDetailItemsDTO> list = this.handleDynamicItemsByAmount(staffSalaryDetailDTO.getTotalExpensesList());
            billDetailDTO.setTotalExpensesList(list);
        }
    }

    /**
     * 中石化结算单处理费用项总金额
     * @param dtoList
     * @return
     */
    private List<HrBillDetailItemsDTO> handleDynamicItemsByAmount(List<HrBillDetailItemsDTO> dtoList) {
        List<HrBillDetailItemsDTO> list = new ArrayList<>();
        Map<String, List<HrBillDetailItemsDTO>> collect = dtoList.stream().collect(Collectors.groupingBy(HrBillDetailItemsDTO::getExpenseName));
        for (String expenseName : collect.keySet()) {
            List<HrBillDetailItemsDTO> hrBillDetailItemsDTOS = collect.get(expenseName);
            HrBillDetailItemsDTO hrBillDetailItemsDTO = hrBillDetailItemsDTOS.get(0);
            BigDecimal bigDecimal = hrBillDetailItemsDTOS.stream().filter(ls -> ls.getAmount() != null).map(HrBillDetailItemsDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            hrBillDetailItemsDTO.setAmount(bigDecimal);
            list.add(hrBillDetailItemsDTO);
        }
        return list;
    }

    /**
     * 创建结算单信息
     * @param hrSettleAccountDTO
     */
    @Override
    public void preservationSettlementDocumentInfo(HrSettleAccountDTO hrSettleAccountDTO) {
        List<String> billIdList = hrSettleAccountDTO.getBillIdList();
        List<HrBillDetailDTO> hrBillDetailDTOList = hrSettleAccountDTO.getHrBillDetailDTO();
        List<String> paymentDate = hrBillDetailDTOList.stream().map(HrBillDetailDTO::getPaymentDate).distinct().collect(Collectors.toList());
        String payDate = paymentDate.get(0);
        String[] split = payDate.split("-");
        HrFeeReview hrFeeReview = new HrFeeReview();
        hrFeeReview.setClientId(hrSettleAccountDTO.getClientId())
            .setBillId(String.join(",",billIdList))
            .setTitle(hrSettleAccountDTO.getTitle())
            .setPayYear(Integer.parseInt(split[0]))
            .setPayMonthly(Integer.parseInt(split[1]))
            .setIsDrawBill(hrSettleAccountDTO.getIsDrawBill())
            .setDetailAppendixId(hrSettleAccountDTO.getDetailAppendixId())
            .setSummaryAppendixId(hrSettleAccountDTO.getSummaryAppendixId())
            .setAppendixId(hrSettleAccountDTO.getAppendixId())
            .setRemark(hrSettleAccountDTO.getRemark())
            .setPaymentDate(String.join(",",paymentDate))
            .setClientType(hrSettleAccountDTO.getClientType())
            .setLastModifiedDate(LocalDateTime.now());
        hrFeeReviewRepository.insert(hrFeeReview);
        //修改账单审核状态以及对应账单锁定不可修改
        this.hrBillRepository.updateReviewStateAndBillState(billIdList,BillEnum.FeeReviewState.TO_BE_REVIEWED.getKey(), BillEnum.BillState.LOCKED.getKey());

        //生成结算单明细
        List<HrBillDetail> hrBillDetailList = new ArrayList<>();
        for (HrBillDetailDTO  hrBillDetailDTO: hrBillDetailDTOList) {
            HrBillDetail hrBillDetail = hrBillDetailMapper.toEntity(hrBillDetailDTO);
            hrBillDetail.setId(null)
                .setBillId(hrFeeReview.getId())
                .setIsUsed(true)
                .setType(4);
            hrBillDetailList.add(hrBillDetail);
        }
        hrBillDetailService.saveBatch(hrBillDetailList);
        //生成结算单汇总
        List<HrBillTotalDTO> hrBillTotalDTOList = hrSettleAccountDTO.getHrBillTotalDTOList();
        if (hrBillTotalDTOList!= null && !hrBillTotalDTOList.isEmpty()){
            hrBillTotalDTOList.forEach(hrBillTotalDTO -> {
                hrBillTotalDTO.setBillId(hrFeeReview.getId())
                    .setPayYear(hrFeeReview.getPayYear())
                    .setPayMonthly(hrFeeReview.getPayMonthly())
                    .setType(BillEnum.BillType.SPECIAL_REVIEW.getKey());
                hrBillTotalRepository.insert(hrBillTotalMapper.toEntity(hrBillTotalDTO));
            });
        }else {
            HrBillTotalDTO hrBillTotalDTO = hrSettleAccountDTO.getHrBillTotalDTO();
            hrBillTotalDTO.setBillId(hrFeeReview.getId())
                .setPayYear(hrFeeReview.getPayYear())
                .setPayMonthly(hrFeeReview.getPayMonthly())
                .setType(BillEnum.BillType.SPECIAL_REVIEW.getKey());
            hrBillTotalRepository.insert(hrBillTotalMapper.toEntity(hrBillTotalDTO));
        }
        if (hrSettleAccountDTO.getReviewChoiceDTOList() != null && !hrSettleAccountDTO.getReviewChoiceDTOList().isEmpty()){
            HrFeeReviewConfig feeReviewConfig = new HrFeeReviewConfig();
            feeReviewConfig
                .setClientId(hrSettleAccountDTO.getClientId())
                .setFeeReviewId(hrFeeReview.getId())
                .setDynamicHeader(JSONObject.toJSONString(hrSettleAccountDTO.getReviewChoiceDTOList()));
            hrFeeReviewConfigRepository.insert(feeReviewConfig);
        }
    }
    /**
     * 查看结算单信息
     * @param id 结算单Id
     * @return
     */
    @Override
    public HrSettleAccountDTO seeSettlementDocumentInfo(String id) {
        HrSettleAccountDTO hrSettleAccountDTO = hrFeeReviewRepository.findHrFeeReviewById(id);
        if (hrSettleAccountDTO.getSummaryAppendixId() != null) {
            List<String> DetailAppendixIds = Arrays.asList(hrSettleAccountDTO.getSummaryAppendixId().split(","));
            List<HrAppendix>  appendixList = this.hrStaffTurnPositiveRepository.selectAppendixId(DetailAppendixIds);
            hrSettleAccountDTO.setSummaryAppendixIdUrl(appendixList);
        }

        if (hrSettleAccountDTO.getAppendixId() != null) {
            List<String> AppendixIds = Arrays.asList(hrSettleAccountDTO.getAppendixId().split(","));
            List<HrAppendix>  appendixList = this.hrStaffTurnPositiveRepository.selectAppendixId(AppendixIds);
            hrSettleAccountDTO.setAppendixIdList(appendixList);
        }
        if (hrSettleAccountDTO.getDetailAppendixId() != null) {
            List<String> DetailAppendixIds = Arrays.asList(hrSettleAccountDTO.getDetailAppendixId().split(","));
            List<HrAppendix>  appendixList = this.hrStaffTurnPositiveRepository.selectAppendixId(DetailAppendixIds);
            hrSettleAccountDTO.setDetailAppendixIdUrl(appendixList);
        }
        if (hrSettleAccountDTO.getDetailPdfAppendixId() != null) {
            List<HrAppendix>  appendixList = this.hrStaffTurnPositiveRepository.selectAppendixId(Collections.singletonList(hrSettleAccountDTO.getDetailPdfAppendixId()));
            hrSettleAccountDTO.setDetailPdfAppendixUrl(appendixList);
        }
        List<String> billIdList = Arrays.asList(hrSettleAccountDTO.getBillId().split(","));
        List<HrBill> billList = hrBillRepository.selectBatchIds(billIdList);
        hrSettleAccountDTO.setHrBillDTO(hrBillMapper.toDto(billList));
        List<String> clientId = billList.stream().map(HrBill::getClientId).collect(Collectors.toList());
        if (clientId == null || clientId.isEmpty()){
            throw new CommonException("未查询到客户信息！");
        }
        List<HrClientDTO> hrClientDTOList = hrClientRepository.findHrClientBatch(clientId);
        hrSettleAccountDTO.setHrClientDTO(hrClientDTOList);
        //结算单信息
        List<HrBillDetailDTO> hrBillDetailDTOList = new ArrayList();
        if (billList.get(0).getBillType().equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey())){
            List<HrBillDetail> hrBillDetailList = hrBillDetailRepository.selectList(new QueryWrapper<HrBillDetail>().eq("bill_id",id).eq("is_delete",0));
            hrBillDetailDTOList = hrBillDetailMapper.toDto(hrBillDetailList);
            hrSettleAccountDTO.setBillType(3);
            List<HrBillTotalDTO> hrBillTotalDTOList = hrBillTotalRepository.getBillTotalByBillId(Collections.singletonList(id));
            hrSettleAccountDTO.setHrBillTotalDTOList(hrBillTotalDTOList);
        }else {
            HrClient rootParentClient = hrClientRepository.getRootParentClient(hrSettleAccountDTO.getClientId());
            if (rootParentClient.getId().equals(SpecialBillClient.HAIER.getKey())){
                hrSettleAccountDTO.setBillType(2);
            }else {
                hrSettleAccountDTO.setBillType(1);
            }
            hrBillDetailDTOList = hrBillDetailRepository.selectSalaryDetail(id);
            //汇总账单
            HrBillTotal hrBillTotal = hrBillTotalRepository.selectOne(new QueryWrapper<HrBillTotal>().eq("bill_id", id).last("LIMIT 1"));
            HrBillTotalDTO billTotalDTO = hrBillTotalMapper.toDto(hrBillTotal);
            if (billTotalDTO == null){
                HrBillTotalDTO billTotalByBatchBill = hrBillTotalRepository.getBillTotalByBatchBill(billIdList);
                if (billTotalByBatchBill == null){
                    throw new CommonException("该结算单不存在账单汇总信息！请查看账单信息查看具体详情");
                }else {
                    billTotalDTO = billTotalByBatchBill;
                }
            }
            hrSettleAccountDTO.setHrBillTotalDTO(billTotalDTO);
        }
        for (HrBillDetailDTO hrBillDetailDTO : hrBillDetailDTOList) {
            if (hrBillDetailDTO.getReason() != null) {
                List<String> billDetailIds = Arrays.asList(hrBillDetailDTO.getReason().split(","));
                if (!billDetailIds.isEmpty()) {
                    HrStaffSalaryDetailDTO hrStaffSalaryDetailDTO = new HrStaffSalaryDetailDTO();
                    hrBillDetailService.assignmentStaffSalaryDetail(hrStaffSalaryDetailDTO, billDetailIds);
                    hrBillDetailDTO.setWageIncrease(hrStaffSalaryDetailDTO.getWageIncrease())
                        .setWageDeduction(hrStaffSalaryDetailDTO.getWageDeduction())
                        .setOtherExpenses(hrStaffSalaryDetailDTO.getOtherExpenses())
                        .setChargeItems(hrStaffSalaryDetailDTO.getChargeItems())
                        .setRefundItems(hrStaffSalaryDetailDTO.getRefundItems());

                    this.setDynamicItemsValue(hrBillDetailDTO, hrStaffSalaryDetailDTO);
                }
            }
        }
        hrSettleAccountDTO.setHrBillDetailDTO(hrBillDetailDTOList);
        //返回该账单选择的账单表头
        HrFeeReviewConfig hrFeeReviewConfig = hrFeeReviewRepository.selectReviewConfigByClientId(null, id);
        if (hrFeeReviewConfig != null){
            hrSettleAccountDTO.setReviewChoiceDTOList(hrFeeReviewConfig.getHrReviewChoiceDTOS());
        }
        return hrSettleAccountDTO;
    }

    /**
     * 查看客户选择的结算单表头
     * @param clientId
     * @return
     */
    @Override
    public List<HrReviewChoiceDTO> handleChoiceDynamic(String clientId) {
        HrFeeReviewConfig hrFeeReviewConfig = hrFeeReviewRepository.selectReviewConfigByClientId(clientId, null);
        if (hrFeeReviewConfig == null){
            return new ArrayList<>();
        }
        return hrFeeReviewConfig.getHrReviewChoiceDTOS();
    }

    /**
     * excel导入工资明细,获取表头
     * @param file
     * @param clientId
     * @return
     */
    @Override
    public Map<String, Object> getOriginTableHeader(MultipartFile file, String clientId) {
        List<String> expenseTypes = Arrays.asList(
            DynamicFeeTypesEnum.NAME.getKey(),
            DynamicFeeTypesEnum.ID_CARD.getKey(),
            DynamicFeeTypesEnum.UNIT_PENSION_CARDINAL.getKey(),
            DynamicFeeTypesEnum.UNIT_UNEMPLOYMENT_CARDINAL.getKey(),
            DynamicFeeTypesEnum.WORK_INJURY_CARDINAL.getKey(),
            DynamicFeeTypesEnum.MEDICAL_INSURANCE_CARDINAL.getKey(),
            DynamicFeeTypesEnum.UNIT_MATERNITY_CARDINAL.getKey(),
            DynamicFeeTypesEnum.PERSONAL_PENSION_CARDINAL.getKey(),
            DynamicFeeTypesEnum.PERSONAL_UNEMPLOYMENT_CARDINAL.getKey(),
            DynamicFeeTypesEnum.MEDICAL_INSURANCE_CARDINAL_PERSONAL.getKey(),
            DynamicFeeTypesEnum.PERSONAL_MATERNITY_CARDINAL.getKey(),
            DynamicFeeTypesEnum.UNIT_PENSION.getKey(),
            DynamicFeeTypesEnum.UNIT_UNEMPLOYMENT.getKey(),
            DynamicFeeTypesEnum.UNIT_MEDICAL.getKey(),
            DynamicFeeTypesEnum.UNIT_INJURY.getKey(),
            DynamicFeeTypesEnum.UNIT_MATERNITY.getKey(),
            DynamicFeeTypesEnum.UNIT_LARGE_MEDICAL_EXPENSE.getKey(),
            DynamicFeeTypesEnum.REPLENISH_WORK_INJURY_EXPENSE.getKey(),
            DynamicFeeTypesEnum.COMMERCIAL_INSURANCE.getKey(),
            DynamicFeeTypesEnum.UNIT_ENTERPRISE_ANNUITY.getKey(),
            DynamicFeeTypesEnum.UNIT_SOCIAL_SECURITY_MAKE_UP.getKey(),
            DynamicFeeTypesEnum.SUBTOTAL_UNIT_SOCIAL_SECURITY.getKey(),
            DynamicFeeTypesEnum.PERSONAL_PENSION.getKey(),
            DynamicFeeTypesEnum.PERSONAL_UNEMPLOYMENT.getKey(),
            DynamicFeeTypesEnum.PERSONAL_MEDICAL.getKey(),
            DynamicFeeTypesEnum.PERSONAL_MATERNITY.getKey(),
            DynamicFeeTypesEnum.PERSONAL_LARGE_MEDICAL_EXPENSE.getKey(),
            DynamicFeeTypesEnum.PERSONAL_ENTERPRISE_ANNUITY.getKey(),
            DynamicFeeTypesEnum.PERSONAL_SOCIAL_SECURITY_MAKE_UP.getKey(),
            DynamicFeeTypesEnum.SUBTOTAL_PERSONAL_SOCIAL_SECURITY.getKey(),
            DynamicFeeTypesEnum.TOTAL_SOCIAL_SECURITY.getKey(),
            DynamicFeeTypesEnum.ACCUMULATION_FUND_BASE.getKey(),
            DynamicFeeTypesEnum.UNIT_ACCUMULATION_FUND_HAIER.getKey(),
            DynamicFeeTypesEnum.PERSONAL_ACCUMULATION_FUND_HAIER.getKey(),
            DynamicFeeTypesEnum.TOTAL_ACCUMULATION_FUND.getKey(),
            DynamicFeeTypesEnum.SALARY.getKey(),
            DynamicFeeTypesEnum.PRE_TAX_SALARY.getKey(),
            DynamicFeeTypesEnum.PERSONAL_TAX.getKey(),
            DynamicFeeTypesEnum.REAL_SALARY.getKey(),
            DynamicFeeTypesEnum.SERVICE_CHARGE.getKey(),
            DynamicFeeTypesEnum.TOTAL_AMOUNT.getKey(),
            DynamicFeeTypesEnum.ENTERPRISE_TAX.getKey(),
            DynamicFeeTypesEnum.SPECIAL_OTHER.getKey(),
            DynamicFeeTypesEnum.MEMBERSHIP_FEE.getKey()
        );
        List<String> idStrs = new ArrayList<>();
        idStrs.add("身份证");
        idStrs.add("证件号");
        idStrs.add("证照号");
        BillTableHeaderDTO result = hrBillDynamicFieldsService.getTableHeaderResult(file, expenseTypes, clientId, idStrs,0,null);
        // 薪酬原单上传到私有云
        HrAppendixDTO hrAppendixDTO;
        hrAppendixDTO  = this.hrAppendixService.uploadSingleFile(file);

        // 保存薪酬账单字段信息
        HrBillDynamicFields billDynamicFields = new HrBillDynamicFields()
            .setContent(JSON.toJSONString(result.getBillFieldInfoDTO(), SerializerFeature.DisableCircularReferenceDetect))
            .setDataStartRows(result.getDataStartRow())
            .setOriginBillUrl(hrAppendixDTO.getFileUrl())
            .setOriginBillName(hrAppendixDTO.getOriginName());
        hrBillDynamicFieldsService.save(billDynamicFields);
        Map<String, Object> map = JSONObject.parseObject(JSONObject.toJSONString(result), Map.class);
        map.put("dynamicFieldId",billDynamicFields.getId());
        return map;
    }

    /**
     * 海尔账单字段映射
     * @param dynamicFieldId 原单ID
     * @param type 0添加 1删除
     * @param param
     */
    @Override
    public List<List<CellItem>> handleMappingRelationWithBill(String dynamicFieldId, Integer type, HrExpenseManageDTO param) {
        HrBillDynamicFieldsDTO dynamicFieldsDTO = this.hrBillDynamicFieldsService.getHrBillDynamicFields(dynamicFieldId);
        if (type == 0){
            return hrBillDynamicFieldsService.handleFieldMappingRelation(dynamicFieldsDTO, param);
        }else {
            return hrBillDynamicFieldsService.handleDelMappingRelation(dynamicFieldsDTO, param);
        }
    }

    /**
     * 获取未映射的字段列表
     * @param dynamicFieldId 原单ID
     * @return
     */
    @Override
    public List<CellItem> getUnUsedFieldList(String dynamicFieldId) {
        HrBillDynamicFieldsDTO dynamicFieldsDTO = this.hrBillDynamicFieldsService.getHrBillDynamicFields(dynamicFieldId);
        if (dynamicFieldsDTO == null){
            throw new CommonException("未查询到薪酬原单！");
        }
        List<CellItem> unUsedFields = dynamicFieldsDTO.getBillFieldInfoDTO().getUnUsedCols();
        // 按照order排序
        unUsedFields.sort((x, y) -> Integer.compare(x.getOrder(), y.getOrder()));
        return unUsedFields;
    }

    /**
     * 返回结算单明细
     *
     * @param hrFeeReviewDTO
     * @return
     */
    @Override
    public Map<String, Object> fillReviewDetail(HrFeeReviewDTO hrFeeReviewDTO) {
        HrBillDynamicFieldsDTO dynamicFieldsDTO = this.hrBillDynamicFieldsService.getHrBillDynamicFields(hrFeeReviewDTO.getDynamicFieldsId());
        if (StringUtils.isEmpty(dynamicFieldsDTO.getOriginBillUrl())) {
            throw new CommonException("未查询到薪酬原单,请重新上传!");
        }
        Map<String, Object> hashMap = new HashMap<>();
        // 解析工作簿的数据,得到map结构的数据
        BillFieldInfoDTO billFieldInfoDTO = dynamicFieldsDTO.getBillFieldInfoDTO();
        List<HrBillDetailDTO> hrBillDetailDTOList = this.parseExcel2Map(billFieldInfoDTO, hrFeeReviewDTO, dynamicFieldsDTO.getDataStartRows(), dynamicFieldsDTO.getOriginBillUrl(), dynamicFieldsDTO.getOriginBillUrl());
        hashMap.put("hrBillDetailDTOList", hrBillDetailDTOList);
        try {
            Map<String, BigDecimal> decimalMap = CalculateUtils.calculateMap(hrBillDetailDTOList, HrBillDetailDTO.class);
            HrBillTotalDTO billTotalDTO = JSONObject.parseObject(JSONObject.toJSONString(decimalMap), HrBillTotalDTO.class);
            billTotalDTO.setStaffNum(hrBillDetailDTOList.size());
            hashMap.put("billTotalDTO", billTotalDTO);
        } catch (Exception e) {
            throw new CommonException("数据解析出现异常!");
        }
        return hashMap;
    }

    /**
     *  根据映射字段解析excel文件,得到map结构
     *  一个身份证对应多条信息
     * @param fieldInfoDTO 字段映射信息
     * @param startRow 数据开始行
     * @param fileName excel名称,必须要带有格式
     * @param fileUrl excel可访问的url
     */
    public List<HrBillDetailDTO> parseExcel2Map(BillFieldInfoDTO fieldInfoDTO,HrFeeReviewDTO hrFeeReviewDTO, int startRow, String fileName, String fileUrl) {
        //获取所有员工信息
        List<String> clientIds = hrClientService.querySubordinateClient(SpecialBillClient.HAIER.getKey());
        List<HrEmployeeWelfareDTO> hrEmployeeWelfareDTOS = hrTalentStaffRepository.selectExportListByIds(new HrEmployeeWelfareDTO(), clientIds);
        List<HrBillDetailDTO> list = new ArrayList<>();
        Workbook workbook;
        // 从已映射的表头,获取姓名,身份证,工资动态项
        ExcelParseDynamicFieldsDTO excelParseDTO = BillFieldInfoDTO.getExcelParseDTO(fieldInfoDTO);
        // 身份证
        HrExpenseManageDTO idCardItem  = excelParseDTO.getIdCardItem();
        String type = "身份证";
        if(idCardItem == null) {
            throw new CommonException("必须配置 [" + type + "] 类型的字段,否则无法解析!");
        }
        // 获取所有的映射字段
        List<HrExpenseManageDTO> mappingFields = fieldInfoDTO.getMappingFields();
        // 非计算列
        List<DynamicFeeTypesEnum> notFeeTypes = Arrays.asList(
            DynamicFeeTypesEnum.NAME,
            DynamicFeeTypesEnum.ID_CARD,
            DynamicFeeTypesEnum.CUSTOMER_NAME,
            DynamicFeeTypesEnum.CURRENT_YEAR,
            DynamicFeeTypesEnum.PROJECT_CODE,
            DynamicFeeTypesEnum.PROJECT_NAME,
            DynamicFeeTypesEnum.PROJECT_TYPE,
            DynamicFeeTypesEnum.CONTRACT_NO,
            DynamicFeeTypesEnum.PEOPLE_NUM,
            DynamicFeeTypesEnum.CENTER_NAME
        );
        Map<String, List<HrExpenseManageDTO>> collect = mappingFields.stream().collect(Collectors.groupingBy(HrExpenseManageDTO::getExpenseType));
        for (Map.Entry<String, List<HrExpenseManageDTO>> stringListEntry : collect.entrySet()) {
            DynamicFeeTypesEnum typesEnum = EnumUtils.getEnumByKey(DynamicFeeTypesEnum.class, stringListEntry.getKey());
            if (typesEnum == null){
                continue;
            }
            List<HrExpenseManageDTO> value = stringListEntry.getValue();
            if (notFeeTypes.contains(typesEnum) && value.size() > 1){
                throw new CommonException("身份标识类型只能配置一个字段,否则无法解析!");
            }
        }
        // 获取数据开始行
        try {
            workbook = ExcelReadUtils.createWorkBook(fileName, FileUtil.getFileInputStream(fileUrl));
        } catch (IOException e) {
            throw new CommonException("数据原单解析失败!");
        }
        // 获取第一个不隐藏的sheet
        Sheet sheet = ExcelReadUtils.getNotHiddenSheetAt(workbook, 0);
        boolean flag = true;
        for (; flag; startRow++) {
            // excel数据解析对象
            Row row = sheet.getRow(startRow);
            String idCard;
            try {
                idCard = BillParseUtils.getCellValue(row, idCardItem.getColIndex()).toUpperCase();
            }catch (Exception e){
                idCard = null;
            }
            if (StringUtils.isEmpty(idCard)) {
                // 查询下一行是否还是空值
                Row nextRow = sheet.getRow(startRow + 1);
                String nextIdCard = BillParseUtils.getCellValue(nextRow, idCardItem.getColIndex());
                if (StringUtils.isEmpty(nextIdCard)) {
                    flag = false;
                }else {
                    throw new CommonException("数据明细存在身份证号码为空的数据，无法继续解析数据!");
                }
            }
            // 数据过滤,只处理符合条件的数据
            boolean filter = BillParseUtils.filterData(row, fieldInfoDTO.getFilterCols());
            if(!filter) {
                continue;
            }
            //匹配系统中员工信息
            String finalIdCard = idCard;
            HrEmployeeWelfareDTO welfareDTO = hrEmployeeWelfareDTOS.stream().filter(lst -> lst.getCertificateNum().equals(finalIdCard)).findAny().orElse(null);

            // 所有的映射字段转成map结构
            Map<String, Object> mapData = BillParseUtils.getMapDataByRow(row, mappingFields, notFeeTypes);
            HrBillDetailDTO billDetailDTO = JSONObject.parseObject(JSONObject.toJSONString(mapData), HrBillDetailDTO.class);
            if (welfareDTO != null){
                billDetailDTO
                    .setPayYear(hrFeeReviewDTO.getPayYear())
                    .setPayMonthly(hrFeeReviewDTO.getPayMonthly())
                    .setStaffId(welfareDTO.getId())
                    .setSystemNum(welfareDTO.getSystemNum())
                    .setPhone(welfareDTO.getPhone())
                    .setStaffStatus(welfareDTO.getStaffStatus())
                    .setPersonnelType(welfareDTO.getPersonnelType())
                    .setIzInsured(welfareDTO.getIzInsured())
                    .setSocialSecurityArea(welfareDTO.getSocialSecurityArea());
            }
            //计算单位社保小计
            if (billDetailDTO.getUnitSubtotal() == null){
                BigDecimal unitSubtotal = CalculateUtils.decimalListAddition(billDetailDTO.getUnitPension(), billDetailDTO.getUnitUnemployment(),
                    billDetailDTO.getWorkInjury(), billDetailDTO.getUnitMedical(), billDetailDTO.getUnitMaternity(), billDetailDTO.getUnitSocialSecurityMakeUp(),
                    billDetailDTO.getUnitLargeMedicalExpense(), billDetailDTO.getReplenishWorkInjuryExpense(), billDetailDTO.getUnitEnterpriseAnnuity(),
                    billDetailDTO.getCommercialInsurance(), billDetailDTO.getUnitLateFee(), billDetailDTO.getUnitOtherFee());
                billDetailDTO.setUnitSubtotal(unitSubtotal);
            }
            //计算个人社保小计
            if (billDetailDTO.getPersonalSubtotal() == null){
                BigDecimal personalSubtotal = CalculateUtils.decimalListAddition(billDetailDTO.getPersonalPension(), billDetailDTO.getPersonalUnemployment(),
                    billDetailDTO.getPersonalMaternity(), billDetailDTO.getPersonalMedical(), billDetailDTO.getPersonalSocialSecurityMakeUp(),
                    billDetailDTO.getPersonalLargeMedicalExpense(), billDetailDTO.getPersonalEnterpriseAnnuity(), billDetailDTO.getPersonalOtherFee());
                billDetailDTO.setPersonalSubtotal(personalSubtotal);
            }
            //计算社保总金额
            if (billDetailDTO.getSocialSecurityTotal() == null){
                billDetailDTO.setSocialSecurityTotal(CalculateUtils.decimalAddition(billDetailDTO.getUnitSubtotal(), billDetailDTO.getPersonalSubtotal()));
            }
            //计算公积金总金额
            if (billDetailDTO.getAccumulationFundTotal() == null){
                billDetailDTO.setAccumulationFundTotal(CalculateUtils.decimalListAddition(billDetailDTO.getUnitAccumulationFund(),billDetailDTO.getUnitAccumulationFundMakeUp(),
                    billDetailDTO.getPersonalAccumulationFund(), billDetailDTO.getPersonalAccumulationFundMakeUp()));
            }
            //计算税前应发 = 应发工资-个人社保小计-个人公积金小计-个人公积金补差
            if (billDetailDTO.getPreTaxSalary() == null){
                billDetailDTO.setPreTaxSalary(CalculateUtils.decimalListSubstract(billDetailDTO.getSalary(), billDetailDTO.getPersonalSubtotal(),
                    billDetailDTO.getPersonalAccumulationFund(), billDetailDTO.getPersonalAccumulationFundMakeUp()));
            }
            billDetailDTO.setSortValue(startRow);
            list.add(billDetailDTO);
        }
        return list;
    }

    /**
     * 处理结算单Excel 和 PDF
     *
     * @param hrFeeReviewDTO
     * @return
     */
    @Override
    public HrSettleAccountDTO handleFeeReviewExcelAndPDF(HrFeeReviewDTO hrFeeReviewDTO) {
        HrFeeReview hrFeeReview = hrFeeReviewRepository.selectById(hrFeeReviewDTO.getId());
        List<String> billIds = Arrays.asList(hrFeeReview.getBillId().split(","));
        HrBill hrBill = hrBillRepository.selectOne(new QueryWrapper<HrBill>().in("id", billIds).orderByDesc("created_date").last("LIMIT 1"));
        hrFeeReview.setDetailAppendixId(hrFeeReviewDTO.getDetailAppendixId());
        HrAppendixDTO hrAppendixDTO = hrAppendixService.getHrAppendix(hrFeeReviewDTO.getDetailAppendixId());
        if (hrFeeReview.getDetailPdfAppendixId() != null){
            try {
                HrSeals hrSeals = hrSealsRepository.selectOne(new QueryWrapper<HrSeals>().eq("is_delete",0).eq("seal_name","财务专用章")
                    .orderByDesc("created_date").last("LIMIT 1"));
                int x = 520;
                int y = 480;
                if (hrBill.getBillType().equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey())){
                    x = 495;
                    y = 460;
                }
                String pdfFileUrl = this.hrBillInvoiceService.excelToPdf(hrFeeReview, hrAppendixDTO, hrBill);
                HrAppendix uploadTempPdf = hrAppendixService.uploadTempPdf(pdfFileUrl);
                String contractNum = RandomUtil.generateNo();
                this.eCloudComponent.uploadContractUrl(contractNum, uploadTempPdf.getOriginName(), uploadTempPdf.getFileUrl());
                this.eCloudComponent.electronicBillSignature(contractNum,uploadTempPdf.getFileUrl(),hrSeals.getSignId(),x, y);
                HrAppendix hrAppendix = this.eCloudComponent.downloadContract(contractNum, CompanyInfoEnum.FIRST_PART_PHONE.getValue());
                hrFeeReview.setContractNum(contractNum);
                hrFeeReview.setDetailPdfAppendixId(hrAppendix.getId());
            } catch (Exception e) {
                throw new CommonException("EXCEL转PDF异常！异常提示【" + e.getMessage() + "】");
            }
        }
        hrFeeReviewRepository.updateById(hrFeeReview);
        return this.seeSettlementDocumentInfo(hrFeeReviewDTO.getId());
    }

}
